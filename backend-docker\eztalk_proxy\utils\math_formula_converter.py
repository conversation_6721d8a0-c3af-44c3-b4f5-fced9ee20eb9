"""
Math Formula Converter
专门处理数学公式格式转换的工具类
基于 Cherry Studio 的实现优化
"""

import re
import logging
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger("EzTalkProxy.Utils.MathFormulaConverter")


class MathFormulaConverter:
    """
    数学公式转换器
    支持多种数学公式格式的转换和优化
    """
    
    def __init__(self):
        # LaTeX 到 Markdown 数学符号映射
        self.latex_symbols = {
            # 希腊字母
            r'\\alpha': r'\\alpha',
            r'\\beta': r'\\beta', 
            r'\\gamma': r'\\gamma',
            r'\\delta': r'\\delta',
            r'\\epsilon': r'\\epsilon',
            r'\\zeta': r'\\zeta',
            r'\\eta': r'\\eta',
            r'\\theta': r'\\theta',
            r'\\iota': r'\\iota',
            r'\\kappa': r'\\kappa',
            r'\\lambda': r'\\lambda',
            r'\\mu': r'\\mu',
            r'\\nu': r'\\nu',
            r'\\xi': r'\\xi',
            r'\\pi': r'\\pi',
            r'\\rho': r'\\rho',
            r'\\sigma': r'\\sigma',
            r'\\tau': r'\\tau',
            r'\\upsilon': r'\\upsilon',
            r'\\phi': r'\\phi',
            r'\\chi': r'\\chi',
            r'\\psi': r'\\psi',
            r'\\omega': r'\\omega',
            
            # 大写希腊字母
            r'\\Gamma': r'\\Gamma',
            r'\\Delta': r'\\Delta',
            r'\\Theta': r'\\Theta',
            r'\\Lambda': r'\\Lambda',
            r'\\Xi': r'\\Xi',
            r'\\Pi': r'\\Pi',
            r'\\Sigma': r'\\Sigma',
            r'\\Upsilon': r'\\Upsilon',
            r'\\Phi': r'\\Phi',
            r'\\Psi': r'\\Psi',
            r'\\Omega': r'\\Omega',
            
            # 数学符号
            r'\\infty': r'\\infty',
            r'\\partial': r'\\partial',
            r'\\nabla': r'\\nabla',
            r'\\pm': r'\\pm',
            r'\\mp': r'\\mp',
            r'\\times': r'\\times',
            r'\\div': r'\\div',
            r'\\cdot': r'\\cdot',
            r'\\leq': r'\\leq',
            r'\\geq': r'\\geq',
            r'\\neq': r'\\neq',
            r'\\approx': r'\\approx',
            r'\\equiv': r'\\equiv',
            r'\\in': r'\\in',
            r'\\notin': r'\\notin',
            r'\\subset': r'\\subset',
            r'\\supset': r'\\supset',
            r'\\subseteq': r'\\subseteq',
            r'\\supseteq': r'\\supseteq',
            r'\\cup': r'\\cup',
            r'\\cap': r'\\cap',
            r'\\emptyset': r'\\emptyset',
            r'\\forall': r'\\forall',
            r'\\exists': r'\\exists',
            r'\\rightarrow': r'\\rightarrow',
            r'\\leftarrow': r'\\leftarrow',
            r'\\leftrightarrow': r'\\leftrightarrow',
            r'\\Rightarrow': r'\\Rightarrow',
            r'\\Leftarrow': r'\\Leftarrow',
            r'\\Leftrightarrow': r'\\Leftrightarrow',

            # 微积分符号
            r'\\int': r'\\int',
            r'\\iint': r'\\iint',
            r'\\iiint': r'\\iiint',
            r'\\oint': r'\\oint',
            r'\\sum': r'\\sum',
            r'\\prod': r'\\prod',
            r'\\lim': r'\\lim',
            r'\\limsup': r'\\limsup',
            r'\\liminf': r'\\liminf',

            # 特殊函数符号
            r'\\sqrt': r'\\sqrt',
            r'\\frac': r'\\frac',
            r'\\binom': r'\\binom',
            r'\\choose': r'\\choose',

            # 数学常数
            r'\\e': r'\\mathrm{e}',
            r'\\i': r'\\mathrm{i}',
            r'\\j': r'\\mathrm{j}',
        }
        
        # 常见数学函数
        self.math_functions = {
            # 三角函数
            r'\\sin': r'\\sin',
            r'\\cos': r'\\cos',
            r'\\tan': r'\\tan',
            r'\\cot': r'\\cot',
            r'\\sec': r'\\sec',
            r'\\csc': r'\\csc',

            # 反三角函数
            r'\\arcsin': r'\\arcsin',
            r'\\arccos': r'\\arccos',
            r'\\arctan': r'\\arctan',
            r'\\arccot': r'\\arccot',
            r'\\arcsec': r'\\arcsec',
            r'\\arccsc': r'\\arccsc',

            # 双曲函数
            r'\\sinh': r'\\sinh',
            r'\\cosh': r'\\cosh',
            r'\\tanh': r'\\tanh',
            r'\\coth': r'\\coth',
            r'\\sech': r'\\sech',
            r'\\csch': r'\\csch',

            # 反双曲函数
            r'\\arcsinh': r'\\arcsinh',
            r'\\arccosh': r'\\arccosh',
            r'\\arctanh': r'\\arctanh',
            r'\\arccoth': r'\\arccoth',
            r'\\arcsech': r'\\arcsech',
            r'\\arccsch': r'\\arccsch',

            # 对数和指数函数
            r'\\log': r'\\log',
            r'\\ln': r'\\ln',
            r'\\lg': r'\\lg',
            r'\\exp': r'\\exp',

            # 极限和优化函数
            r'\\lim': r'\\lim',
            r'\\max': r'\\max',
            r'\\min': r'\\min',
            r'\\sup': r'\\sup',
            r'\\inf': r'\\inf',
            r'\\arg': r'\\arg',
            r'\\argmax': r'\\argmax',
            r'\\argmin': r'\\argmin',

            # 特殊函数
            r'\\gcd': r'\\gcd',
            r'\\lcm': r'\\lcm',
            r'\\deg': r'\\deg',
            r'\\det': r'\\det',
            r'\\dim': r'\\dim',
            r'\\ker': r'\\ker',
            r'\\rank': r'\\rank',
            r'\\trace': r'\\trace',
        }
    
    def convert_latex_to_markdown(self, text: str) -> str:
        """
        将 LaTeX 格式的数学公式转换为 Markdown 格式
        
        主要转换：
        - \\[...\\] -> $$...$$  (块级公式)
        - \\(...\\) -> $...$    (行内公式)
        """
        if not text or ('\\[' not in text and '\\(' not in text):
            return text
        
        # 简单的替换方法（适用于大多数情况）
        result = text
        
        # 转换块级公式
        result = re.sub(r'\\\\?\[', '$$', result)
        result = re.sub(r'\\\\?\]', '$$', result)
        
        # 转换行内公式
        result = re.sub(r'\\\\?\(', '$', result)
        result = re.sub(r'\\\\?\)', '$', result)
        
        return result
    
    def enhance_math_formulas(self, text: str) -> str:
        """
        增强数学公式格式
        修复常见的格式问题
        """
        if '$' not in text:
            return text

        result = text

        # 1. 修复分数格式
        result = self._fix_fractions(result)

        # 2. 修复根号格式
        result = self._fix_square_roots(result)

        # 3. 修复上下标格式
        result = self._fix_superscripts_subscripts(result)

        # 4. 修复求和和积分
        result = self._fix_sum_integral(result)

        # 5. 修复数学函数
        result = self._fix_math_functions(result)

        # 6. 修复矩阵和向量
        result = self._fix_matrices(result)

        # 7. 修复泰勒级数和幂级数
        result = self._fix_taylor_series(result)

        # 8. 修复阶乘和组合数
        result = self._fix_factorial_combinations(result)

        # 9. 修复导数和微分
        result = self._fix_derivatives(result)

        # 10. 增强中文数学表达式
        result = self.enhance_chinese_math_expressions(result)

        # 11. 清理空格
        result = self._clean_math_spacing(result)

        # 12. 处理欧拉公式和复数
        result = self._fix_euler_formula(result)

        # 13. 清理多余的反斜杠
        result = self._clean_excessive_backslashes(result)

        return result
    
    def _fix_fractions(self, text: str) -> str:
        """修复分数格式"""
        # 修复 \frac 格式
        text = re.sub(r'\\frac\s+(\w+)\s+(\w+)', r'\\frac{\1}{\2}', text)
        text = re.sub(r'\\frac\s*\{\s*([^}]+)\s*\}\s*\{\s*([^}]+)\s*\}', r'\\frac{\1}{\2}', text)
        
        # 将简单的分数转换为 \frac 格式
        text = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', text)
        text = re.sub(r'([a-zA-Z]+)/([a-zA-Z]+)', r'\\frac{\1}{\2}', text)
        
        return text
    
    def _fix_square_roots(self, text: str) -> str:
        """修复根号格式"""
        # 修复 \sqrt 格式
        text = re.sub(r'\\sqrt\s+(\w+)', r'\\sqrt{\1}', text)
        text = re.sub(r'\\sqrt\s*\{\s*([^}]+)\s*\}', r'\\sqrt{\1}', text)
        
        # 将 sqrt() 转换为 \sqrt{}
        text = re.sub(r'sqrt\(([^)]+)\)', r'\\sqrt{\1}', text)
        
        return text
    
    def _fix_superscripts_subscripts(self, text: str) -> str:
        """修复上下标格式"""
        # 修复上标
        text = re.sub(r'([a-zA-Z0-9])\s*\^\s*([^{\s]+)', r'\1^{\2}', text)
        text = re.sub(r'([a-zA-Z0-9])\s*\^\s*\{([^}]+)\}', r'\1^{\2}', text)
        
        # 修复下标
        text = re.sub(r'([a-zA-Z0-9])\s*_\s*([^{\s]+)', r'\1_{\2}', text)
        text = re.sub(r'([a-zA-Z0-9])\s*_\s*\{([^}]+)\}', r'\1_{\2}', text)
        
        # 修复同时有上下标的情况
        text = re.sub(r'([a-zA-Z0-9])\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)', r'\1_{\2}^{\3}', text)
        text = re.sub(r'([a-zA-Z0-9])\s*\^\s*([^{\s]+)\s*_\s*([^{\s]+)', r'\1^{\2}_{\3}', text)
        
        return text
    
    def _fix_sum_integral(self, text: str) -> str:
        """修复求和和积分格式"""
        # 修复求和
        text = re.sub(r'\\sum\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)', r'\\sum_{\1}^{\2}', text)
        text = re.sub(r'\\sum\s*\^\s*([^{\s]+)\s*_\s*([^{\s]+)', r'\\sum_{\2}^{\1}', text)
        
        # 修复积分
        text = re.sub(r'\\int\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)', r'\\int_{\1}^{\2}', text)
        text = re.sub(r'\\int\s*\^\s*([^{\s]+)\s*_\s*([^{\s]+)', r'\\int_{\2}^{\1}', text)
        
        # 修复其他运算符
        text = re.sub(r'\\prod\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)', r'\\prod_{\1}^{\2}', text)
        text = re.sub(r'\\lim\s*_\s*([^{\s]+)', r'\\lim_{\1}', text)
        
        return text
    
    def _fix_math_functions(self, text: str) -> str:
        """修复数学函数格式"""
        # 修复三角函数 - 只在没有反斜杠的情况下添加
        for func in ['sin', 'cos', 'tan', 'cot', 'sec', 'csc']:
            # 避免重复添加反斜杠
            text = re.sub(f'(?<!\\\\){func}\\s*\\(([^)]+)\\)', f'\\\\{func}(\\1)', text)
            text = re.sub(f'(?<!\\\\){func}\\s+([^\\s]+)', f'\\\\{func} \\1', text)

        # 修复反三角函数
        for func in ['arcsin', 'arccos', 'arctan', 'arccot', 'arcsec', 'arccsc']:
            text = re.sub(f'(?<!\\\\){func}\\s*\\(([^)]+)\\)', f'\\\\{func}(\\1)', text)
            text = re.sub(f'(?<!\\\\){func}\\s+([^\\s]+)', f'\\\\{func} \\1', text)

        # 修复双曲函数
        for func in ['sinh', 'cosh', 'tanh', 'coth', 'sech', 'csch']:
            text = re.sub(f'(?<!\\\\){func}\\s*\\(([^)]+)\\)', f'\\\\{func}(\\1)', text)
            text = re.sub(f'(?<!\\\\){func}\\s+([^\\s]+)', f'\\\\{func} \\1', text)

        # 修复对数函数
        text = re.sub(r'(?<!\\)log\s*\(([^)]+)\)', r'\\log(\1)', text)
        text = re.sub(r'(?<!\\)ln\s*\(([^)]+)\)', r'\\ln(\1)', text)

        # 修复指数函数
        text = re.sub(r'(?<!\\)exp\s*\(([^)]+)\)', r'\\exp(\1)', text)

        return text
    
    def _fix_matrices(self, text: str) -> str:
        """修复矩阵和向量格式"""
        # 修复 begin/end 环境
        text = re.sub(r'\\begin\s*\{\s*([a-zA-Z]+)\s*\}', r'\\begin{\1}', text)
        text = re.sub(r'\\end\s*\{\s*([a-zA-Z]+)\s*\}', r'\\end{\1}', text)
        
        # 修复常见矩阵环境
        matrix_envs = ['matrix', 'pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix']
        for env in matrix_envs:
            text = re.sub(f'\\\\begin\\s*\\{{\\s*{env}\\s*\\}}', f'\\\\begin{{{env}}}', text)
            text = re.sub(f'\\\\end\\s*\\{{\\s*{env}\\s*\\}}', f'\\\\end{{{env}}}', text)
        
        return text

    def _fix_taylor_series(self, text: str) -> str:
        """修复泰勒级数和幂级数格式"""
        # 修复阶乘表示法
        text = re.sub(r'(\d+)!', r'\1!', text)  # 确保阶乘符号紧贴数字
        text = re.sub(r'([a-zA-Z]+)!', r'\1!', text)  # 变量的阶乘
        text = re.sub(r'\(([^)]+)\)!', r'(\1)!', text)  # 括号内表达式的阶乘

        # 修复 n 次导数表示法
        text = re.sub(r'f\s*\^\s*\(\s*(\w+)\s*\)\s*\(([^)]+)\)', r'f^{(\1)}(\2)', text)
        text = re.sub(r'([a-zA-Z])\s*\^\s*\(\s*(\w+)\s*\)\s*\(([^)]+)\)', r'\1^{(\2)}(\3)', text)

        # 修复泰勒级数的通项
        text = re.sub(r'\\frac\s*\{\s*([^}]+)\s*\}\s*\{\s*(\w+)!\s*\}', r'\\frac{\1}{\2!}', text)
        text = re.sub(r'\\frac\s*\{\s*([^}]+)\s*\}\s*\{\s*(\d+)!\s*\}', r'\\frac{\1}{\2!}', text)

        # 修复幂级数的系数
        text = re.sub(r'([a-zA-Z])\s*_\s*(\w+)\s*([a-zA-Z])\s*\^\s*(\w+)', r'\1_{\2}\3^{\4}', text)

        return text

    def _fix_factorial_combinations(self, text: str) -> str:
        """修复阶乘和组合数格式"""
        # 修复组合数 C(n,k) 或 nCk
        text = re.sub(r'C\s*\(\s*(\w+)\s*,\s*(\w+)\s*\)', r'C(\1,\2)', text)
        text = re.sub(r'(\w+)C(\w+)', r'\1C\2', text)
        text = re.sub(r'\\binom\s*\{\s*([^}]+)\s*\}\s*\{\s*([^}]+)\s*\}', r'\\binom{\1}{\2}', text)

        # 修复排列数 P(n,k) 或 nPk
        text = re.sub(r'P\s*\(\s*(\w+)\s*,\s*(\w+)\s*\)', r'P(\1,\2)', text)
        text = re.sub(r'(\w+)P(\w+)', r'\1P\2', text)

        # 修复双阶乘
        text = re.sub(r'(\w+)!!\s*', r'\1!!', text)

        return text

    def _fix_derivatives(self, text: str) -> str:
        """修复导数和微分格式"""
        # 修复一阶导数
        text = re.sub(r"([a-zA-Z])'", r"\1'", text)
        text = re.sub(r"([a-zA-Z])''", r"\1''", text)

        # 修复高阶导数
        text = re.sub(r'([a-zA-Z])\s*\^\s*\(\s*(\d+)\s*\)', r'\1^{(\2)}', text)

        # 修复偏导数
        text = re.sub(r'\\partial\s+([a-zA-Z])', r'\\partial \1', text)
        text = re.sub(r'\\frac\s*\{\s*\\partial\s*([^}]+)\s*\}\s*\{\s*\\partial\s*([^}]+)\s*\}',
                     r'\\frac{\\partial \1}{\\partial \2}', text)

        # 修复全微分
        text = re.sub(r'd\s*([a-zA-Z])', r'd\1', text)
        text = re.sub(r'\\frac\s*\{\s*d\s*([^}]+)\s*\}\s*\{\s*d\s*([^}]+)\s*\}',
                     r'\\frac{d\1}{d\2}', text)

        # 修复微分算子
        text = re.sub(r'\\nabla\s*([a-zA-Z])', r'\\nabla \1', text)
        text = re.sub(r'\\Delta\s*([a-zA-Z])', r'\\Delta \1', text)

        return text

    def _clean_math_spacing(self, text: str) -> str:
        """清理数学公式中的空格"""
        # 清理 $ 符号周围的空格
        text = re.sub(r'\$\s+', '$', text)
        text = re.sub(r'\s+\$', '$', text)
        text = re.sub(r'\$\$\s+', '$$', text)
        text = re.sub(r'\s+\$\$', '$$', text)

        # 清理大括号内的空格
        text = re.sub(r'\{\s+', '{', text)
        text = re.sub(r'\s+\}', '}', text)

        # 清理反斜杠后的空格
        text = re.sub(r'\\\s+([a-zA-Z])', r'\\\1', text)

        # 清理多余的反斜杠（避免 \\\\func 变成 \\func）
        text = re.sub(r'\\\\\\\\([a-zA-Z]+)', r'\\\\\1', text)  # \\\\func -> \\func
        text = re.sub(r'\\\\\\([a-zA-Z]+)', r'\\\1', text)      # \\\func -> \func

        return text

    def _fix_euler_formula(self, text: str) -> str:
        """修复欧拉公式和复数表示"""
        # 修复欧拉公式 e^(ix) = cos(x) + i sin(x)
        text = re.sub(r'e\s*\^\s*\(\s*i\s*\*?\s*([^)]+)\s*\)', r'e^{i\1}', text)
        text = re.sub(r'e\s*\^\s*\{\s*i\s*\*?\s*([^}]+)\s*\}', r'e^{i\1}', text)

        # 修复复数单位 i
        text = re.sub(r'(?<![a-zA-Z])i(?![a-zA-Z])', r'i', text)  # 保持 i 为斜体

        # 修复常数 π
        text = re.sub(r'(?<!\\)pi(?![a-zA-Z])', r'\\pi', text)
        text = re.sub(r'π', r'\\pi', text)

        # 修复欧拉恒等式
        text = re.sub(r'e\s*\^\s*\(\s*i\s*\*?\s*\\?pi\s*\)', r'e^{i\\pi}', text)
        text = re.sub(r'e\s*\^\s*\{\s*i\s*\*?\s*\\?pi\s*\}', r'e^{i\\pi}', text)

        # 修复三角函数在欧拉公式中的表示
        text = re.sub(r'cos\s*\(\s*([^)]+)\s*\)\s*\+\s*i\s*\*?\s*sin\s*\(\s*([^)]+)\s*\)',
                     r'\\cos(\1) + i\\sin(\2)', text)

        return text

    def _clean_excessive_backslashes(self, text: str) -> str:
        """清理多余的反斜杠"""
        # 清理数学函数前的多余反斜杠
        math_functions = [
            'sin', 'cos', 'tan', 'cot', 'sec', 'csc',
            'arcsin', 'arccos', 'arctan', 'arccot', 'arcsec', 'arccsc',
            'sinh', 'cosh', 'tanh', 'coth', 'sech', 'csch',
            'log', 'ln', 'exp', 'sqrt', 'frac',
            'sum', 'prod', 'int', 'lim', 'partial'
        ]

        for func in math_functions:
            # 将多个反斜杠替换为单个反斜杠
            text = re.sub(f'\\\\\\\\+{func}', f'\\\\{func}', text)

        # 清理一般的多余反斜杠
        text = re.sub(r'\\\\\\\\+', r'\\\\', text)  # 4个或更多反斜杠 -> 2个反斜杠
        text = re.sub(r'\\\\\\([a-zA-Z])', r'\\\\\1', text)  # 3个反斜杠 -> 2个反斜杠

        return text

    def _clean_excessive_backslashes(self, text: str) -> str:
        """清理多余的反斜杠"""
        # 清理数学函数前的多余反斜杠
        math_functions = [
            'sin', 'cos', 'tan', 'cot', 'sec', 'csc',
            'arcsin', 'arccos', 'arctan', 'arccot', 'arcsec', 'arccsc',
            'sinh', 'cosh', 'tanh', 'coth', 'sech', 'csch',
            'log', 'ln', 'exp', 'sqrt', 'frac',
            'sum', 'prod', 'int', 'lim', 'partial', 'pi', 'infty'
        ]

        for func in math_functions:
            # 将多个反斜杠替换为单个反斜杠
            text = re.sub(f'\\\\\\\\+{func}', f'\\\\{func}', text)
            text = re.sub(f'\\\\\\\\\\\\{func}', f'\\\\{func}', text)  # 处理更多反斜杠

        # 清理一般的多余反斜杠
        text = re.sub(r'\\\\\\\\+', r'\\\\', text)  # 4个或更多反斜杠 -> 2个反斜杠
        text = re.sub(r'\\\\\\([a-zA-Z])', r'\\\\\1', text)  # 3个反斜杠 -> 2个反斜杠

        # 特别处理欧拉公式中的常见问题
        text = re.sub(r'\\\\\\\\cos', r'\\\\cos', text)
        text = re.sub(r'\\\\\\\\sin', r'\\\\sin', text)
        text = re.sub(r'\\\\\\\\pi', r'\\\\pi', text)

        return text

    def enhance_chinese_math_expressions(self, text: str) -> str:
        """
        增强中文数学表达式处理
        处理中文数学术语和表达式
        """
        # 暂时简化中文处理，避免复杂的正则表达式问题
        # 只处理一些基本的中文数学表达式格式

        # 处理中文数学表达式的特殊格式
        # 例如："设 f(x) = e^x" 中的中文
        text = re.sub(r'设\s+([a-zA-Z])\s*\(([^)]+)\)\s*=', r'设 $\1(\2) =$', text)
        text = re.sub(r'令\s+([a-zA-Z])\s*=', r'令 $\1 =$', text)
        text = re.sub(r'因此\s+([a-zA-Z])\s*=', r'因此 $\1 =$', text)
        text = re.sub(r'所以\s+([a-zA-Z])\s*=', r'所以 $\1 =$', text)

        return text

    def balance_math_delimiters(self, text: str) -> str:
        """
        平衡数学分隔符
        确保 $ 和 $$ 符号成对出现
        """
        # 统计单个 $ 符号
        single_dollar_count = 0
        result = []
        i = 0
        
        while i < len(text):
            if i < len(text) - 1 and text[i:i+2] == '$$':
                result.append('$$')
                i += 2
            elif text[i] == '$':
                single_dollar_count += 1
                result.append('$')
                i += 1
            else:
                result.append(text[i])
                i += 1
        
        # 如果单个 $ 符号数量是奇数，添加一个 $
        if single_dollar_count % 2 == 1:
            result.append('$')
        
        return ''.join(result)
    
    def detect_math_content(self, text: str) -> Dict[str, int]:
        """
        检测文本中的数学内容
        返回统计信息
        """
        stats = {
            'inline_math': 0,      # 行内数学公式数量
            'block_math': 0,       # 块级数学公式数量
            'latex_commands': 0,   # LaTeX 命令数量
            'greek_letters': 0,    # 希腊字母数量
            'math_functions': 0,   # 数学函数数量
        }
        
        # 统计行内数学公式
        stats['inline_math'] = len(re.findall(r'\$[^$]+\$', text))
        
        # 统计块级数学公式
        stats['block_math'] = len(re.findall(r'\$\$[^$]+\$\$', text))
        
        # 统计 LaTeX 命令
        stats['latex_commands'] = len(re.findall(r'\\[a-zA-Z]+', text))
        
        # 统计希腊字母
        for symbol in self.latex_symbols:
            stats['greek_letters'] += len(re.findall(symbol, text))
        
        # 统计数学函数
        for func in self.math_functions:
            stats['math_functions'] += len(re.findall(func, text))
        
        return stats
    
    def is_valid_math_expression(self, expression: str) -> bool:
        """
        检查是否是有效的数学表达式
        """
        if not expression or not expression.strip():
            return False
        
        # 检查是否包含数学符号或函数
        math_indicators = [
            r'\\[a-zA-Z]+',  # LaTeX 命令
            r'\^',           # 上标
            r'_',            # 下标
            r'\\frac',       # 分数
            r'\\sqrt',       # 根号
            r'\\sum',        # 求和
            r'\\int',        # 积分
            r'[+\-*/=<>]',   # 基本运算符
        ]
        
        for pattern in math_indicators:
            if re.search(pattern, expression):
                return True
        
        return False


# 创建全局实例
math_formula_converter = MathFormulaConverter()