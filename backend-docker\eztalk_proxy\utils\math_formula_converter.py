"""
Math Formula Converter
专门处理数学公式格式转换的工具类
基于 Cherry Studio 的实现优化
"""

import re
import logging
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger("EzTalkProxy.Utils.MathFormulaConverter")


class MathFormulaConverter:
    """
    数学公式转换器
    支持多种数学公式格式的转换和优化
    """
    
    def __init__(self):
        # LaTeX 到 Markdown 数学符号映射
        self.latex_symbols = {
            # 希腊字母
            r'\\alpha': r'\\alpha',
            r'\\beta': r'\\beta', 
            r'\\gamma': r'\\gamma',
            r'\\delta': r'\\delta',
            r'\\epsilon': r'\\epsilon',
            r'\\zeta': r'\\zeta',
            r'\\eta': r'\\eta',
            r'\\theta': r'\\theta',
            r'\\iota': r'\\iota',
            r'\\kappa': r'\\kappa',
            r'\\lambda': r'\\lambda',
            r'\\mu': r'\\mu',
            r'\\nu': r'\\nu',
            r'\\xi': r'\\xi',
            r'\\pi': r'\\pi',
            r'\\rho': r'\\rho',
            r'\\sigma': r'\\sigma',
            r'\\tau': r'\\tau',
            r'\\upsilon': r'\\upsilon',
            r'\\phi': r'\\phi',
            r'\\chi': r'\\chi',
            r'\\psi': r'\\psi',
            r'\\omega': r'\\omega',
            
            # 大写希腊字母
            r'\\Gamma': r'\\Gamma',
            r'\\Delta': r'\\Delta',
            r'\\Theta': r'\\Theta',
            r'\\Lambda': r'\\Lambda',
            r'\\Xi': r'\\Xi',
            r'\\Pi': r'\\Pi',
            r'\\Sigma': r'\\Sigma',
            r'\\Upsilon': r'\\Upsilon',
            r'\\Phi': r'\\Phi',
            r'\\Psi': r'\\Psi',
            r'\\Omega': r'\\Omega',
            
            # 数学符号
            r'\\infty': r'\\infty',
            r'\\partial': r'\\partial',
            r'\\nabla': r'\\nabla',
            r'\\pm': r'\\pm',
            r'\\mp': r'\\mp',
            r'\\times': r'\\times',
            r'\\div': r'\\div',
            r'\\cdot': r'\\cdot',
            r'\\leq': r'\\leq',
            r'\\geq': r'\\geq',
            r'\\neq': r'\\neq',
            r'\\approx': r'\\approx',
            r'\\equiv': r'\\equiv',
            r'\\in': r'\\in',
            r'\\notin': r'\\notin',
            r'\\subset': r'\\subset',
            r'\\supset': r'\\supset',
            r'\\subseteq': r'\\subseteq',
            r'\\supseteq': r'\\supseteq',
            r'\\cup': r'\\cup',
            r'\\cap': r'\\cap',
            r'\\emptyset': r'\\emptyset',
            r'\\forall': r'\\forall',
            r'\\exists': r'\\exists',
            r'\\rightarrow': r'\\rightarrow',
            r'\\leftarrow': r'\\leftarrow',
            r'\\leftrightarrow': r'\\leftrightarrow',
            r'\\Rightarrow': r'\\Rightarrow',
            r'\\Leftarrow': r'\\Leftarrow',
            r'\\Leftrightarrow': r'\\Leftrightarrow',
        }
        
        # 常见数学函数
        self.math_functions = {
            r'\\sin': r'\\sin',
            r'\\cos': r'\\cos',
            r'\\tan': r'\\tan',
            r'\\cot': r'\\cot',
            r'\\sec': r'\\sec',
            r'\\csc': r'\\csc',
            r'\\arcsin': r'\\arcsin',
            r'\\arccos': r'\\arccos',
            r'\\arctan': r'\\arctan',
            r'\\sinh': r'\\sinh',
            r'\\cosh': r'\\cosh',
            r'\\tanh': r'\\tanh',
            r'\\log': r'\\log',
            r'\\ln': r'\\ln',
            r'\\lg': r'\\lg',
            r'\\exp': r'\\exp',
            r'\\lim': r'\\lim',
            r'\\max': r'\\max',
            r'\\min': r'\\min',
            r'\\sup': r'\\sup',
            r'\\inf': r'\\inf',
        }
    
    def convert_latex_to_markdown(self, text: str) -> str:
        """
        将 LaTeX 格式的数学公式转换为 Markdown 格式
        
        主要转换：
        - \\[...\\] -> $$...$$  (块级公式)
        - \\(...\\) -> $...$    (行内公式)
        """
        if not text or ('\\[' not in text and '\\(' not in text):
            return text
        
        # 简单的替换方法（适用于大多数情况）
        result = text
        
        # 转换块级公式
        result = re.sub(r'\\\\?\[', '$$', result)
        result = re.sub(r'\\\\?\]', '$$', result)
        
        # 转换行内公式
        result = re.sub(r'\\\\?\(', '$', result)
        result = re.sub(r'\\\\?\)', '$', result)
        
        return result
    
    def enhance_math_formulas(self, text: str) -> str:
        """
        增强数学公式格式
        修复常见的格式问题
        """
        if '$' not in text:
            return text
        
        result = text
        
        # 1. 修复分数格式
        result = self._fix_fractions(result)
        
        # 2. 修复根号格式
        result = self._fix_square_roots(result)
        
        # 3. 修复上下标格式
        result = self._fix_superscripts_subscripts(result)
        
        # 4. 修复求和和积分
        result = self._fix_sum_integral(result)
        
        # 5. 修复数学函数
        result = self._fix_math_functions(result)
        
        # 6. 修复矩阵和向量
        result = self._fix_matrices(result)
        
        # 7. 清理空格
        result = self._clean_math_spacing(result)
        
        return result
    
    def _fix_fractions(self, text: str) -> str:
        """修复分数格式"""
        # 修复 \frac 格式
        text = re.sub(r'\\frac\s+(\w+)\s+(\w+)', r'\\frac{\1}{\2}', text)
        text = re.sub(r'\\frac\s*\{\s*([^}]+)\s*\}\s*\{\s*([^}]+)\s*\}', r'\\frac{\1}{\2}', text)
        
        # 将简单的分数转换为 \frac 格式
        text = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', text)
        text = re.sub(r'([a-zA-Z]+)/([a-zA-Z]+)', r'\\frac{\1}{\2}', text)
        
        return text
    
    def _fix_square_roots(self, text: str) -> str:
        """修复根号格式"""
        # 修复 \sqrt 格式
        text = re.sub(r'\\sqrt\s+(\w+)', r'\\sqrt{\1}', text)
        text = re.sub(r'\\sqrt\s*\{\s*([^}]+)\s*\}', r'\\sqrt{\1}', text)
        
        # 将 sqrt() 转换为 \sqrt{}
        text = re.sub(r'sqrt\(([^)]+)\)', r'\\sqrt{\1}', text)
        
        return text
    
    def _fix_superscripts_subscripts(self, text: str) -> str:
        """修复上下标格式"""
        # 修复上标
        text = re.sub(r'([a-zA-Z0-9])\s*\^\s*([^{\s]+)', r'\1^{\2}', text)
        text = re.sub(r'([a-zA-Z0-9])\s*\^\s*\{([^}]+)\}', r'\1^{\2}', text)
        
        # 修复下标
        text = re.sub(r'([a-zA-Z0-9])\s*_\s*([^{\s]+)', r'\1_{\2}', text)
        text = re.sub(r'([a-zA-Z0-9])\s*_\s*\{([^}]+)\}', r'\1_{\2}', text)
        
        # 修复同时有上下标的情况
        text = re.sub(r'([a-zA-Z0-9])\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)', r'\1_{\2}^{\3}', text)
        text = re.sub(r'([a-zA-Z0-9])\s*\^\s*([^{\s]+)\s*_\s*([^{\s]+)', r'\1^{\2}_{\3}', text)
        
        return text
    
    def _fix_sum_integral(self, text: str) -> str:
        """修复求和和积分格式"""
        # 修复求和
        text = re.sub(r'\\sum\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)', r'\\sum_{\1}^{\2}', text)
        text = re.sub(r'\\sum\s*\^\s*([^{\s]+)\s*_\s*([^{\s]+)', r'\\sum_{\2}^{\1}', text)
        
        # 修复积分
        text = re.sub(r'\\int\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)', r'\\int_{\1}^{\2}', text)
        text = re.sub(r'\\int\s*\^\s*([^{\s]+)\s*_\s*([^{\s]+)', r'\\int_{\2}^{\1}', text)
        
        # 修复其他运算符
        text = re.sub(r'\\prod\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)', r'\\prod_{\1}^{\2}', text)
        text = re.sub(r'\\lim\s*_\s*([^{\s]+)', r'\\lim_{\1}', text)
        
        return text
    
    def _fix_math_functions(self, text: str) -> str:
        """修复数学函数格式"""
        # 修复三角函数
        for func in ['sin', 'cos', 'tan', 'cot', 'sec', 'csc']:
            text = re.sub(f'{func}\\s*\\(([^)]+)\\)', f'\\\\{func}(\\1)', text)
            text = re.sub(f'{func}\\s+([^\\s]+)', f'\\\\{func} \\1', text)
        
        # 修复对数函数
        text = re.sub(r'log\s*\(([^)]+)\)', r'\\log(\1)', text)
        text = re.sub(r'ln\s*\(([^)]+)\)', r'\\ln(\1)', text)
        text = re.sub(r'\\log\s+([^{\s]+)', r'\\log{\1}', text)
        text = re.sub(r'\\ln\s+([^{\s]+)', r'\\ln{\1}', text)
        
        return text
    
    def _fix_matrices(self, text: str) -> str:
        """修复矩阵和向量格式"""
        # 修复 begin/end 环境
        text = re.sub(r'\\begin\s*\{\s*([a-zA-Z]+)\s*\}', r'\\begin{\1}', text)
        text = re.sub(r'\\end\s*\{\s*([a-zA-Z]+)\s*\}', r'\\end{\1}', text)
        
        # 修复常见矩阵环境
        matrix_envs = ['matrix', 'pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix']
        for env in matrix_envs:
            text = re.sub(f'\\\\begin\\s*\\{{\\s*{env}\\s*\\}}', f'\\\\begin{{{env}}}', text)
            text = re.sub(f'\\\\end\\s*\\{{\\s*{env}\\s*\\}}', f'\\\\end{{{env}}}', text)
        
        return text
    
    def _clean_math_spacing(self, text: str) -> str:
        """清理数学公式中的空格"""
        # 清理 $ 符号周围的空格
        text = re.sub(r'\$\s+', '$', text)
        text = re.sub(r'\s+\$', '$', text)
        text = re.sub(r'\$\$\s+', '$$', text)
        text = re.sub(r'\s+\$\$', '$$', text)
        
        # 清理大括号内的空格
        text = re.sub(r'\{\s+', '{', text)
        text = re.sub(r'\s+\}', '}', text)
        
        # 清理反斜杠后的空格
        text = re.sub(r'\\\s+([a-zA-Z])', r'\\\1', text)
        
        return text
    
    def balance_math_delimiters(self, text: str) -> str:
        """
        平衡数学分隔符
        确保 $ 和 $$ 符号成对出现
        """
        # 统计单个 $ 符号
        single_dollar_count = 0
        result = []
        i = 0
        
        while i < len(text):
            if i < len(text) - 1 and text[i:i+2] == '$$':
                result.append('$$')
                i += 2
            elif text[i] == '$':
                single_dollar_count += 1
                result.append('$')
                i += 1
            else:
                result.append(text[i])
                i += 1
        
        # 如果单个 $ 符号数量是奇数，添加一个 $
        if single_dollar_count % 2 == 1:
            result.append('$')
        
        return ''.join(result)
    
    def detect_math_content(self, text: str) -> Dict[str, int]:
        """
        检测文本中的数学内容
        返回统计信息
        """
        stats = {
            'inline_math': 0,      # 行内数学公式数量
            'block_math': 0,       # 块级数学公式数量
            'latex_commands': 0,   # LaTeX 命令数量
            'greek_letters': 0,    # 希腊字母数量
            'math_functions': 0,   # 数学函数数量
        }
        
        # 统计行内数学公式
        stats['inline_math'] = len(re.findall(r'\$[^$]+\$', text))
        
        # 统计块级数学公式
        stats['block_math'] = len(re.findall(r'\$\$[^$]+\$\$', text))
        
        # 统计 LaTeX 命令
        stats['latex_commands'] = len(re.findall(r'\\[a-zA-Z]+', text))
        
        # 统计希腊字母
        for symbol in self.latex_symbols:
            stats['greek_letters'] += len(re.findall(symbol, text))
        
        # 统计数学函数
        for func in self.math_functions:
            stats['math_functions'] += len(re.findall(func, text))
        
        return stats
    
    def is_valid_math_expression(self, expression: str) -> bool:
        """
        检查是否是有效的数学表达式
        """
        if not expression or not expression.strip():
            return False
        
        # 检查是否包含数学符号或函数
        math_indicators = [
            r'\\[a-zA-Z]+',  # LaTeX 命令
            r'\^',           # 上标
            r'_',            # 下标
            r'\\frac',       # 分数
            r'\\sqrt',       # 根号
            r'\\sum',        # 求和
            r'\\int',        # 积分
            r'[+\-*/=<>]',   # 基本运算符
        ]
        
        for pattern in math_indicators:
            if re.search(pattern, expression):
                return True
        
        return False


# 创建全局实例
math_formula_converter = MathFormulaConverter()