package com.example.everytalk.util

/**
 * 数学公式渲染器 - 基于Cherry Studio的数学引擎配置
 * 
 * 支持多种数学渲染引擎：
 * 1. MathJax - 功能强大，支持复杂公式
 * 2. KaTeX - 渲染速度快，轻量级
 * 3. 原生Unicode - 简单符号替换
 */
object MathFormulaRenderer {
    
    /**
     * 数学引擎类型
     */
    enum class MathEngine {
        MATHJAX,
        KATEX,
        UNICODE,
        NONE
    }
    
    /**
     * 渲染配置
     */
    data class RenderConfig(
        val mathEngine: MathEngine = MathEngine.MATHJAX,
        val enableInlineMath: Boolean = true,
        val enableDisplayMath: Boolean = true,
        val enableMhchem: Boolean = true, // 化学公式支持
        val enableCopyTex: Boolean = true, // 复制LaTeX支持
        val theme: String = "default"
    )
    
    /**
     * 生成MathJax配置的HTML头部
     */
    fun generateMathJaxHeader(config: RenderConfig = RenderConfig()): String {
        return """
            <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
            <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
            <script>
                window.MathJax = {
                    tex: {
                        inlineMath: [['$', '$'], ['\\(', '\\)']],
                        displayMath: [['$$', '$$'], ['\\[', '\\]']],
                        processEscapes: true,
                        processEnvironments: true,
                        ${if (config.enableMhchem) "packages: {'[+]': ['mhchem']}," else ""}
                        macros: {
                            // 常用数学宏定义
                            RR: "\\mathbb{R}",
                            NN: "\\mathbb{N}",
                            ZZ: "\\mathbb{Z}",
                            QQ: "\\mathbb{Q}",
                            CC: "\\mathbb{C}",
                            dd: "\\mathrm{d}",
                            ee: "\\mathrm{e}",
                            ii: "\\mathrm{i}",
                            jj: "\\mathrm{j}",
                            // 向量和矩阵
                            vec: ["\\boldsymbol{#1}", 1],
                            mat: ["\\boldsymbol{#1}", 1],
                            // 常用运算符
                            grad: "\\nabla",
                            curl: "\\nabla \\times",
                            divergence: "\\nabla \\cdot",
                            laplacian: "\\nabla^2"
                        }
                    },
                    options: {
                        ignoreHtmlClass: "tex2jax_ignore",
                        processHtmlClass: "tex2jax_process"
                    },
                    chtml: {
                        scale: 1.0,
                        minScale: 0.5,
                        matchFontHeight: false,
                        fontURL: 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/output/chtml/fonts/woff-v2'
                    },
                    startup: {
                        ready: () => {
                            MathJax.startup.defaultReady();
                            console.log('MathJax is loaded and ready.');
                        }
                    }
                };
            </script>
        """.trimIndent()
    }
    
    /**
     * 生成KaTeX配置的HTML头部
     */
    fun generateKaTeXHeader(config: RenderConfig = RenderConfig()): String {
        return """
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
            <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
            <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js"></script>
            ${if (config.enableMhchem) """<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/mhchem.min.js"></script>""" else ""}
            ${if (config.enableCopyTex) """<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/copy-tex.min.js"></script>""" else ""}
            <script>
                document.addEventListener("DOMContentLoaded", function() {
                    renderMathInElement(document.body, {
                        delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\[', right: '\\]', display: true},
                            {left: '\\(', right: '\\)', display: false}
                        ],
                        throwOnError: false,
                        errorColor: '#cc0000',
                        strict: false,
                        trust: false,
                        macros: {
                            // 常用数学宏定义
                            "\\RR": "\\mathbb{R}",
                            "\\NN": "\\mathbb{N}",
                            "\\ZZ": "\\mathbb{Z}",
                            "\\QQ": "\\mathbb{Q}",
                            "\\CC": "\\mathbb{C}",
                            "\\dd": "\\mathrm{d}",
                            "\\ee": "\\mathrm{e}",
                            "\\ii": "\\mathrm{i}",
                            "\\jj": "\\mathrm{j}"
                        }
                    });
                });
            </script>
        """.trimIndent()
    }
    
    /**
     * 根据配置生成数学渲染器头部
     */
    fun generateMathHeader(config: RenderConfig = RenderConfig()): String {
        return when (config.mathEngine) {
            MathEngine.MATHJAX -> generateMathJaxHeader(config)
            MathEngine.KATEX -> generateKaTeXHeader(config)
            MathEngine.UNICODE -> generateUnicodeHeader()
            MathEngine.NONE -> ""
        }
    }
    
    /**
     * 生成Unicode数学符号的CSS样式
     */
    private fun generateUnicodeHeader(): String {
        return """
            <style>
                .math-unicode {
                    font-family: 'STIX Two Math', 'Cambria Math', 'Times New Roman', serif;
                    font-size: 1.1em;
                }
                .math-display {
                    display: block;
                    text-align: center;
                    margin: 1em 0;
                    font-size: 1.2em;
                }
                .math-inline {
                    display: inline;
                }
            </style>
        """.trimIndent()
    }
    
    /**
     * 预处理数学公式 - 优化AI输出格式
     */
    fun preprocessMathContent(text: String, config: RenderConfig = RenderConfig()): String {
        var processed = text
        
        // 1. 标准化LaTeX分隔符
        processed = EnhancedMarkdownProcessor.processLatexBrackets(processed)
        
        // 2. 修复常见的AI输出问题
        processed = fixCommonMathIssues(processed)
        
        // 3. 根据引擎类型进行特定处理
        processed = when (config.mathEngine) {
            MathEngine.MATHJAX -> preprocessForMathJax(processed)
            MathEngine.KATEX -> preprocessForKaTeX(processed)
            MathEngine.UNICODE -> convertToUnicode(processed)
            MathEngine.NONE -> processed
        }
        
        return processed
    }
    
    /**
     * 修复常见的数学公式问题
     */
    private fun fixCommonMathIssues(text: String): String {
        var result = text
        
        try {
            // 修复空格问题
            result = result.replace(Regex("""\$\s+([^$]*?)\s+\$""")) { "$${it.groupValues[1].trim()}$" }
            result = result.replace(Regex("""\$\$\s+([^$]*?)\s+\$\$""")) { "$$$${it.groupValues[1].trim()}$$$$" }
            
            // 修复嵌套美元符号
            result = result.replace(Regex("""\$\$([^$]*)\$([^$]*)\$([^$]*)\$\$""")) {
                "$$$${it.groupValues[1]}${it.groupValues[2]}${it.groupValues[3]}$$$$"
            }
        } catch (e: Exception) {
            // 如果正则表达式处理失败，返回原始文本
            println("MathFormulaRenderer: Regex processing failed, using original text: ${e.message}")
        }
        
        // 修复常见的LaTeX命令错误 - 使用普通字符串替换而不是正则表达式
        val fixes = mapOf(
            "\\begin{align}" to "\\begin{align}",
            "\\end{align}" to "\\end{align}",
            "\\begin{equation}" to "\\begin{equation}",
            "\\end{equation}" to "\\end{equation}",
            "\\begin{matrix}" to "\\begin{matrix}",
            "\\end{matrix}" to "\\end{matrix}",
            "\\begin{pmatrix}" to "\\begin{pmatrix}",
            "\\end{pmatrix}" to "\\end{pmatrix}",
            "\\begin{bmatrix}" to "\\begin{bmatrix}",
            "\\end{bmatrix}" to "\\end{bmatrix}"
        )
        
        // 使用简单的字符串替换，避免正则表达式语法错误
        fixes.forEach { (pattern, replacement) ->
            result = result.replace(pattern, replacement, ignoreCase = true)
        }
        
        return result
    }
    
    /**
     * MathJax特定预处理
     */
    private fun preprocessForMathJax(text: String): String {
        var result = text
        
        // MathJax支持更多LaTeX命令，保持原始格式
        // 只需要确保分隔符正确
        result = result.replace("\\[", "$$").replace("\\]", "$$")
        result = result.replace("\\(", "$").replace("\\)", "$")
        
        return result
    }
    
    /**
     * KaTeX特定预处理
     */
    private fun preprocessForKaTeX(text: String): String {
        var result = text
        
        // KaTeX不支持某些LaTeX命令，需要转换
        val katexUnsupported = mapOf(
            "\\begin{align}" to "\\begin{aligned}",
            "\\end{align}" to "\\end{aligned}",
            "\\begin{eqnarray}" to "\\begin{aligned}",
            "\\end{eqnarray}" to "\\end{aligned}"
        )
        
        katexUnsupported.forEach { (unsupported, supported) ->
            result = result.replace(unsupported, supported)
        }
        
        return result
    }
    
    /**
     * 转换为Unicode数学符号
     */
    private fun convertToUnicode(text: String): String {
        var result = text
        
        // 移除LaTeX分隔符，直接显示内容
        result = result.replace(Regex("""\$\$([^$]+)\$\$""")) { 
            """<div class="math-unicode math-display">${convertLatexToUnicode(it.groupValues[1])}</div>""" 
        }
        result = result.replace(Regex("""\$([^$]+)\$""")) { 
            """<span class="math-unicode math-inline">${convertLatexToUnicode(it.groupValues[1])}</span>""" 
        }
        
        return result
    }
    
    /**
     * 将LaTeX命令转换为Unicode符号
     */
    fun convertLatexToUnicode(latex: String): String {
        var result = latex.trim()
        
        // 首先处理复杂的LaTeX结构
        // 处理分数
        result = result.replace(Regex("""\\frac\s*\{([^}]+)\}\s*\{([^}]+)\}""")) { 
            val numerator = convertLatexToUnicode(it.groupValues[1])
            val denominator = convertLatexToUnicode(it.groupValues[2])
            "$numerator/$denominator"
        }
        
        // 处理根号
        result = result.replace(Regex("""\\sqrt\s*\{([^}]+)\}""")) { 
            "√(${convertLatexToUnicode(it.groupValues[1])})"
        }
        result = result.replace(Regex("""\\sqrt\s*\[([^]]+)\]\s*\{([^}]+)\}""")) { 
            val index = it.groupValues[1]
            val radicand = convertLatexToUnicode(it.groupValues[2])
            "∜($radicand)" // 简化处理，实际应该根据index显示不同的根号
        }
        
        // 处理上下标
        result = result.replace(Regex("""([a-zA-Z0-9αβγδεζηθικλμνξπρστυφχψωΓΔΘΛΞΠΣΥΦΨΩ])\^(\{[^}]+\}|[a-zA-Z0-9])""")) { 
            val base = it.groupValues[1]
            val exponent = it.groupValues[2].removeSurrounding("{", "}")
            "$base^$exponent"
        }
        result = result.replace(Regex("""([a-zA-Z0-9αβγδεζηθικλμνξπρστυφχψωΓΔΘΛΞΠΣΥΦΨΩ])_(\{[^}]+\}|[a-zA-Z0-9])""")) { 
            val base = it.groupValues[1]
            val subscript = it.groupValues[2].removeSurrounding("{", "}")
            "$base₍$subscript₎"
        }
        
        val unicodeMap = mapOf(
            // 希腊字母
            "\\alpha" to "α", "\\beta" to "β", "\\gamma" to "γ", "\\delta" to "δ",
            "\\epsilon" to "ε", "\\zeta" to "ζ", "\\eta" to "η", "\\theta" to "θ",
            "\\iota" to "ι", "\\kappa" to "κ", "\\lambda" to "λ", "\\mu" to "μ",
            "\\nu" to "ν", "\\xi" to "ξ", "\\pi" to "π", "\\rho" to "ρ",
            "\\sigma" to "σ", "\\tau" to "τ", "\\upsilon" to "υ", "\\phi" to "φ",
            "\\chi" to "χ", "\\psi" to "ψ", "\\omega" to "ω",
            
            // 大写希腊字母
            "\\Gamma" to "Γ", "\\Delta" to "Δ", "\\Theta" to "Θ", "\\Lambda" to "Λ",
            "\\Xi" to "Ξ", "\\Pi" to "Π", "\\Sigma" to "Σ", "\\Upsilon" to "Υ",
            "\\Phi" to "Φ", "\\Psi" to "Ψ", "\\Omega" to "Ω",
            
            // 数学运算符
            "\\pm" to "±", "\\mp" to "∓", "\\times" to "×", "\\div" to "÷",
            "\\cdot" to "·", "\\ast" to "∗", "\\star" to "⋆", "\\circ" to "∘",
            "\\bullet" to "•", "\\cap" to "∩", "\\cup" to "∪", "\\vee" to "∨",
            "\\wedge" to "∧", "\\oplus" to "⊕", "\\ominus" to "⊖", "\\otimes" to "⊗",
            "\\oslash" to "⊘", "\\odot" to "⊙",
            
            // 关系符号
            "\\leq" to "≤", "\\geq" to "≥", "\\neq" to "≠", "\\approx" to "≈",
            "\\equiv" to "≡", "\\sim" to "∼", "\\simeq" to "≃", "\\cong" to "≅",
            "\\propto" to "∝", "\\parallel" to "∥", "\\perp" to "⊥",
            
            // 集合符号
            "\\in" to "∈", "\\notin" to "∉", "\\subset" to "⊂", "\\supset" to "⊃",
            "\\subseteq" to "⊆", "\\supseteq" to "⊇", "\\emptyset" to "∅",
            "\\varnothing" to "∅",
            
            // 逻辑符号
            "\\land" to "∧", "\\lor" to "∨", "\\lnot" to "¬", "\\neg" to "¬",
            "\\forall" to "∀", "\\exists" to "∃", "\\nexists" to "∄",
            
            // 箭头
            "\\leftarrow" to "←", "\\rightarrow" to "→", "\\leftrightarrow" to "↔",
            "\\Leftarrow" to "⇐", "\\Rightarrow" to "⇒", "\\Leftrightarrow" to "⇔",
            "\\uparrow" to "↑", "\\downarrow" to "↓", "\\updownarrow" to "↕",
            "\\to" to "→", "\\gets" to "←", "\\mapsto" to "↦",
            
            // 微积分
            "\\partial" to "∂", "\\nabla" to "∇", "\\infty" to "∞",
            "\\sum" to "∑", "\\prod" to "∏", "\\int" to "∫", "\\oint" to "∮",
            "\\iint" to "∬", "\\iiint" to "∭", "\\lim" to "lim",
            
            // 几何符号
            "\\angle" to "∠", "\\triangle" to "△", "\\square" to "□", "\\diamond" to "◊",
            
            // 具体三角形符号 - 按字母顺序排列，确保完整覆盖
            "\\triangleABC" to "△ABC", "\\triangleABFE" to "△ABFE", "\\triangleAFP" to "△AFP",
            "\\triangleAPQ" to "△APQ", "\\triangleARQA" to "△ARQA", "\\triangleBEF" to "△BEF",
            "\\triangleBFE" to "△BFE", "\\triangleCDE" to "△CDE", "\\triangleCOQ" to "△COQ",
            "\\triangleDOC" to "△DOC", "\\triangleEOC" to "△EOC", "\\trianglePQR" to "△PQR",
            "\\triangleROA" to "△ROA", "\\triangleXYZ" to "△XYZ",
            
            // 更多常见三角形组合
            "\\triangleABCD" to "△ABCD", "\\triangleABDE" to "△ABDE", "\\triangleACDE" to "△ACDE",
            
            // 更多几何符号
            "\\parallel" to "∥", "\\perp" to "⊥", "\\cong" to "≅", "\\sim" to "∼",
            "\\approx" to "≈", "\\equiv" to "≡", "\\neq" to "≠", "\\leq" to "≤", "\\geq" to "≥",
            
            // 其他符号
            "\\sqrt" to "√", "\\degree" to "°", "\\prime" to "′", "\\circ" to "°",
            "\\hbar" to "ℏ", "\\ell" to "ℓ", "\\wp" to "℘", "\\Re" to "ℜ",
            "\\Im" to "ℑ", "\\aleph" to "ℵ", "\\beth" to "ℶ", "\\gimel" to "ℷ",
            
            // 特殊函数
            "\\sin" to "sin", "\\cos" to "cos", "\\tan" to "tan",
            "\\sec" to "sec", "\\csc" to "csc", "\\cot" to "cot",
            "\\arcsin" to "arcsin", "\\arccos" to "arccos", "\\arctan" to "arctan",
            "\\sinh" to "sinh", "\\cosh" to "cosh", "\\tanh" to "tanh",
            "\\log" to "log", "\\ln" to "ln", "\\exp" to "exp",
            
            // 点和线段
            "\\overline" to "‾", "\\underline" to "_",
            "\\vec" to "⃗", "\\dot" to "·", "\\ddot" to "¨",
            
            // 括号（虽然通常保持原样，但有时需要特殊处理）
            "\\left(" to "(", "\\right)" to ")",
            "\\left[" to "[", "\\right]" to "]",
            "\\left\\{" to "{", "\\right\\}" to "}",
            "\\left|" to "|", "\\right|" to "|",
            
            // 空格和格式
            "\\," to " ", "\\;" to " ", "\\:" to " ", "\\!" to "",
            "\\quad" to "    ", "\\qquad" to "        ",
            "\\\\" to "\n", "\\newline" to "\n"
        )
        
        // 按长度排序，先替换长的命令，避免部分匹配
        val sortedEntries = unicodeMap.entries.sortedByDescending { it.key.length }
        sortedEntries.forEach { (latex, unicode) ->
            result = result.replace(latex, unicode)
        }
        
        // 清理多余的空格和换行
        result = result.replace(Regex("\\s+"), " ").trim()
        
        return result
    }
    
    /**
     * 生成完整的数学渲染HTML
     */
    fun generateMathHTML(content: String, config: RenderConfig = RenderConfig()): String {
        val processedContent = preprocessMathContent(content, config)
        val mathHeader = generateMathHeader(config)
        
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Math Content</title>
                $mathHeader
                <style>
                    body {
                        font-family: 'Times New Roman', serif;
                        line-height: 1.6;
                        margin: 20px;
                        background: white;
                    }
                    .math-content {
                        max-width: 800px;
                        margin: 0 auto;
                    }
                    .error {
                        color: #cc0000;
                        background: #ffe6e6;
                        padding: 10px;
                        border-radius: 4px;
                        margin: 10px 0;
                    }
                </style>
            </head>
            <body>
                <div class="math-content">
                    $processedContent
                </div>
                ${if (config.mathEngine == MathEngine.MATHJAX) generateMathJaxPostScript() else ""}
            </body>
            </html>
        """.trimIndent()
    }
    
    /**
     * 生成MathJax后处理脚本
     */
    private fun generateMathJaxPostScript(): String {
        return """
            <script>
                // 确保MathJax完全加载后再进行渲染
                if (window.MathJax && window.MathJax.typesetPromise) {
                    MathJax.typesetPromise([document.body]).then(() => {
                        console.log('MathJax rendering completed.');
                    }).catch((err) => {
                        console.error('MathJax rendering error:', err);
                    });
                }
            </script>
        """.trimIndent()
    }
    /**
     * 使用MathJax引擎渲染数学公式
     * 在Android环境中，我们使用Unicode转换作为后备方案
     */
    fun renderWithMathJax(content: String): String {
        // 在Android环境中，先应用增强预处理，然后使用Unicode转换
        val enhancedContent = enhancedPreprocessMathFormula(content)
        val cleanedContent = cleanExcessiveBackslashes(enhancedContent)
        return convertLatexToUnicode(cleanedContent)
    }

    /**
     * 使用KaTeX引擎渲染数学公式
     * 在Android环境中，我们使用Unicode转换作为后备方案
     */
    fun renderWithKaTeX(content: String): String {
        // 在Android环境中，先应用增强预处理，然后使用Unicode转换
        val enhancedContent = enhancedPreprocessMathFormula(content)
        val cleanedContent = cleanExcessiveBackslashes(enhancedContent)
        return convertLatexToUnicode(cleanedContent)
    }

    /**
     * 增强数学公式预处理，包含泰勒级数、阶乘等高级数学表达式
     */
    private fun enhancedPreprocessMathFormula(formula: String): String {
        var processed = formula

        // 基本格式修复
        processed = processed.replace(Regex("(\\d+)/(\\d+)"), "\\\\frac{$1}{$2}")
        processed = processed.replace(Regex("\\^(\\d+)"), "^{$1}")
        processed = processed.replace(Regex("\\^([a-zA-Z])"), "^{$1}")
        processed = processed.replace(Regex("_(\\d+)"), "_{$1}")
        processed = processed.replace(Regex("_([a-zA-Z])"), "_{$1}")
        processed = processed.replace(Regex("sqrt\\((.*?)\\)"), "\\\\sqrt{$1}")

        // 增强泰勒级数和阶乘
        processed = enhanceTaylorSeries(processed)

        // 增强数学函数
        processed = enhanceMathFunctions(processed)

        // 增强导数格式
        processed = enhanceDerivatives(processed)

        return processed
    }

    /**
     * 增强泰勒级数和阶乘格式
     */
    private fun enhanceTaylorSeries(formula: String): String {
        var processed = formula

        // 修复阶乘表示法
        processed = processed.replace(Regex("(\\d+)!"), "$1!")
        processed = processed.replace(Regex("([a-zA-Z]+)!"), "$1!")
        processed = processed.replace(Regex("\\(([^)]+)\\)!"), "($1)!")

        // 修复 n 次导数表示法
        processed = processed.replace(Regex("f\\s*\\^\\s*\\(\\s*(\\w+)\\s*\\)\\s*\\(([^)]+)\\)"), "f^{($1)}($2)")
        processed = processed.replace(Regex("([a-zA-Z])\\s*\\^\\s*\\(\\s*(\\w+)\\s*\\)\\s*\\(([^)]+)\\)"), "$1^{($2)}($3)")

        // 修复泰勒级数的通项
        processed = processed.replace(Regex("\\\\frac\\s*\\{\\s*([^}]+)\\s*\\}\\s*\\{\\s*(\\w+)!\\s*\\}"), "\\\\frac{$1}{$2!}")

        return processed
    }

    /**
     * 增强数学函数格式
     */
    private fun enhanceMathFunctions(formula: String): String {
        var processed = formula

        // 修复三角函数
        val trigFunctions = listOf("sin", "cos", "tan", "cot", "sec", "csc")
        for (func in trigFunctions) {
            processed = processed.replace(Regex("$func\\s*\\(([^)]+)\\)"), "\\\\$func($1)")
            processed = processed.replace(Regex("$func\\s+([^\\s]+)"), "\\\\$func $1")
        }

        // 修复反三角函数
        val arcTrigFunctions = listOf("arcsin", "arccos", "arctan", "arccot", "arcsec", "arccsc")
        for (func in arcTrigFunctions) {
            processed = processed.replace(Regex("$func\\s*\\(([^)]+)\\)"), "\\\\$func($1)")
            processed = processed.replace(Regex("$func\\s+([^\\s]+)"), "\\\\$func $1")
        }

        // 修复双曲函数
        val hypFunctions = listOf("sinh", "cosh", "tanh", "coth", "sech", "csch")
        for (func in hypFunctions) {
            processed = processed.replace(Regex("$func\\s*\\(([^)]+)\\)"), "\\\\$func($1)")
            processed = processed.replace(Regex("$func\\s+([^\\s]+)"), "\\\\$func $1")
        }

        // 修复对数和指数函数
        processed = processed.replace(Regex("log\\s*\\(([^)]+)\\)"), "\\\\log($1)")
        processed = processed.replace(Regex("ln\\s*\\(([^)]+)\\)"), "\\\\ln($1)")
        processed = processed.replace(Regex("exp\\s*\\(([^)]+)\\)"), "\\\\exp($1)")

        return processed
    }

    /**
     * 增强导数格式
     */
    private fun enhanceDerivatives(formula: String): String {
        var processed = formula

        // 修复一阶导数
        processed = processed.replace(Regex("([a-zA-Z])'"), "$1'")
        processed = processed.replace(Regex("([a-zA-Z])''"), "$1''")

        // 修复高阶导数
        processed = processed.replace(Regex("([a-zA-Z])\\s*\\^\\s*\\(\\s*(\\d+)\\s*\\)"), "$1^{($2)}")

        // 修复偏导数
        processed = processed.replace(Regex("\\\\partial\\s+([a-zA-Z])"), "\\\\partial $1")
        processed = processed.replace(Regex("\\\\frac\\s*\\{\\s*\\\\partial\\s*([^}]+)\\s*\\}\\s*\\{\\s*\\\\partial\\s*([^}]+)\\s*\\}"),
                                    "\\\\frac{\\\\partial $1}{\\\\partial $2}")

        // 修复全微分
        processed = processed.replace(Regex("d\\s*([a-zA-Z])"), "d$1")
        processed = processed.replace(Regex("\\\\frac\\s*\\{\\s*d\\s*([^}]+)\\s*\\}\\s*\\{\\s*d\\s*([^}]+)\\s*\\}"),
                                    "\\\\frac{d$1}{d$2}")

        return processed
    }
}