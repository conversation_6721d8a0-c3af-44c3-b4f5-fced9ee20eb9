package com.example.everytalk.util

/**
 * 增强的Markdown处理器 - 基于Cherry Studio的优秀实践
 * 
 * 主要功能：
 * 1. 智能LaTeX公式转换
 * 2. 保护代码块和链接
 * 3. 平衡括号算法
 * 4. 数学引擎配置支持
 */
object EnhancedMarkdownProcessor {
    
    /**
     * 数学引擎类型
     */
    enum class MathEngine {
        KATEX,
        MATHJAX,
        NONE
    }
    
    /**
     * 处理配置
     */
    data class ProcessConfig(
        val mathEngine: MathEngine = MathEngine.MATHJAX,
        val enableLatexConversion: Boolean = true,
        val enableCodeProtection: Boolean = true,
        val enableLinkProtection: Boolean = true,
        val forceDollarMath: Boolean = false
    )
    
    /**
     * LaTeX匹配结果
     */
    private data class LatexMatch(
        val start: Int,
        val end: Int,
        val pre: String,
        val body: String,
        val post: String
    )
    
    /**
     * 检查是否包含潜在的LaTeX模式
     */
    private val containsLatexRegex = Regex("""\\[(.*?)\\]|\\((.*?)\\)""", RegexOption.DOT_MATCHES_ALL)
    
    /**
     * 主要处理函数 - 处理LaTeX括号转换
     * 基于Cherry Studio的高级实现，支持嵌套括号和转义字符
     */
    fun processLatexBrackets(text: String, config: ProcessConfig = ProcessConfig()): String {
        if (!config.enableLatexConversion) return text
        
        // 检查是否包含潜在的 LaTeX 模式
        val containsLatexRegex = Regex("\\\\\\(.*?\\\\\\)|\\\\\\[.*?\\\\\\]", RegexOption.DOT_MATCHES_ALL)
        if (!containsLatexRegex.containsMatchIn(text)) {
            return text
        }
        
        // 保护代码块和链接
        val protectedItems = mutableListOf<String>()
        var processedContent = text
        
        if (config.enableCodeProtection) {
            // 保护代码块（包括多行代码块和行内代码）
            processedContent = processedContent.replace(Regex("(```[\\s\\S]*?```|`[^`]*`)")) { match ->
                val index = protectedItems.size
                protectedItems.add(match.value)
                "__CHERRY_STUDIO_PROTECTED_${index}__"
            }
            
            // 保护链接 [text](url)
            processedContent = processedContent.replace(Regex("\\[([^\\[\\]]*(?:\\[[^\\]]*\\][^\\[\\]]*)*)\\]\\([^)]*?\\)")) { match ->
                val index = protectedItems.size
                protectedItems.add(match.value)
                "__CHERRY_STUDIO_PROTECTED_${index}__"
            }
        }
        
        // LaTeX 括号转换函数
        fun processMath(content: String, openDelim: String, closeDelim: String, wrapper: String): String {
            var result = ""
            var remaining = content
            
            while (remaining.isNotEmpty()) {
                val match = findLatexMatch(remaining, openDelim, closeDelim)
                if (match == null) {
                    result += remaining
                    break
                }
                
                result += match.pre
                result += "$wrapper${match.body}$wrapper"
                remaining = match.post
            }
            
            return result
        }
        
        // 先处理块级公式，再处理内联公式
        var result = processMath(processedContent, "\\[", "\\]", "$$")
        result = processMath(result, "\\(", "\\)", "$")
        
        // 还原被保护的内容
        if (config.enableCodeProtection) {
            result = result.replace(Regex("__CHERRY_STUDIO_PROTECTED_(\\d+)__")) { match ->
                val index = match.groupValues[1].toIntOrNull()
                if (index != null && index >= 0 && index < protectedItems.size) {
                    protectedItems[index]
                } else {
                    match.value
                }
            }
        }
        
        return result
    }
    
    /**
     * 处理LaTeX括号 - 将 \[ \] 转换为 $$ $$，将 \( \) 转换为 $ $
     * 基于Cherry Studio的实现，保持向后兼容
     */
    fun processLatexBrackets(text: String): String {
        return processLatexBrackets(text, ProcessConfig())
    }
    
    /**
     * 查找 LaTeX 数学公式的匹配括号对
     * 使用平衡括号算法处理嵌套结构，正确识别转义字符
     */
    private fun findLatexMatch(text: String, openDelim: String, closeDelim: String): LatexMatch? {
        // 统计连续反斜杠：奇数个表示转义，偶数个表示未转义
        fun escaped(i: Int): Boolean {
            var count = 0
            var index = i - 1
            while (index >= 0 && text[index] == '\\') {
                count++
                index--
            }
            return count % 2 == 1
        }
        
        // 查找第一个有效的开始标记
        for (i in 0..text.length - openDelim.length) {
            // 没有找到开始分隔符或被转义，跳过
            if (!text.startsWith(openDelim, i) || escaped(i)) continue
            
            // 处理嵌套结构
            var j = i + openDelim.length
            var depth = 1
            
            while (j <= text.length - closeDelim.length && depth > 0) {
                // 计算当前位置对深度的影响：+1(开始), -1(结束), 0(无关)
                val delta = when {
                    text.startsWith(openDelim, j) && !escaped(j) -> 1
                    text.startsWith(closeDelim, j) && !escaped(j) -> -1
                    else -> 0
                }
                
                if (delta != 0) {
                    depth += delta
                    
                    // 找到了匹配的结束位置
                    if (depth == 0) {
                        return LatexMatch(
                            start = i,
                            end = j + closeDelim.length,
                            pre = text.substring(0, i),
                            body = text.substring(i + openDelim.length, j),
                            post = text.substring(j + closeDelim.length)
                        )
                    }
                    
                    // 跳过已处理的分隔符字符，避免重复检查
                    j += if (delta > 0) openDelim.length - 1 else closeDelim.length - 1
                }
                j++
            }
        }
        
        return null
    }
    

     
     /**
      * 转换数学公式格式（简单版本）
     * 将LaTeX格式的 '\\[' 和 '\\]' 转换为 '$$'
     * 将LaTeX格式的 '\\(' 和 '\\)' 转换为 '$'
     */
    fun convertMathFormula(input: String): String {
        if (input.isEmpty()) return input
        
        var result = input
        result = result.replace("\\[", "$$").replace("\\]", "$$")
        result = result.replace("\\(", "$").replace("\\)", "$")
        return result
    }
    
    /**
     * 移除Markdown文本中每行末尾的两个空格
     */
    fun removeTrailingDoubleSpaces(markdown: String): String {
        return markdown.replace(Regex(" {2}$", RegexOption.MULTILINE), "")
    }
    
    /**
     * 增强的数学公式预处理 - 支持多种AI模型的输出格式
     */
    fun preprocessMathFormulas(text: String): String {
        var result = text
        
        // 1. 标准化LaTeX分隔符
        result = result.replace(Regex("""\\begin\s*\{\s*([a-zA-Z]+)\s*\}""")) { "\\begin{${it.groupValues[1]}}" }
        result = result.replace(Regex("""\\end\s*\{\s*([a-zA-Z]+)\s*\}""")) { "\\end{${it.groupValues[1]}}" }
        
        // 2. 修复常见的数学符号
        val symbolMap = mapOf(
            // 基本运算符
            "\\cdot" to "·",
            "\\times" to "×",
            "\\div" to "÷",
            "\\pm" to "±",
            "\\mp" to "∓",
            
            // 关系符号
            "\\leq" to "≤",
            "\\geq" to "≥",
            "\\neq" to "≠",
            "\\approx" to "≈",
            "\\equiv" to "≡",
            "\\sim" to "∼",
            "\\cong" to "≅",
            "\\parallel" to "∥",
            "\\perp" to "⊥",
            
            // 几何符号
            "\\angle" to "∠",
            "\\triangle" to "△",
            "\\square" to "□",
            "\\diamond" to "◊",
            
            // 具体三角形符号
            "\\triangleABC" to "△ABC",
            "\\triangleABFE" to "△ABFE",
            "\\triangleAFP" to "△AFP",
            "\\triangleAPQ" to "△APQ",
            "\\triangleARQA" to "△ARQA",
            "\\triangleBEF" to "△BEF",
            "\\triangleBFE" to "△BFE",
            "\\triangleCDE" to "△CDE",
            "\\triangleCOQ" to "△COQ",
            "\\triangleDOC" to "△DOC",
            "\\triangleEOC" to "△EOC",
            "\\trianglePQR" to "△PQR",
            "\\triangleROA" to "△ROA",
            "\\triangleXYZ" to "△XYZ",
            
            // 其他符号
            "\\infty" to "∞",
            
            // 希腊字母
            "\\alpha" to "α",
            "\\beta" to "β",
            "\\gamma" to "γ",
            "\\delta" to "δ",
            "\\epsilon" to "ε",
            "\\theta" to "θ",
            "\\lambda" to "λ",
            "\\mu" to "μ",
            "\\pi" to "π",
            "\\sigma" to "σ",
            "\\phi" to "φ",
            "\\omega" to "ω"
        )
        
        symbolMap.forEach { (latex, unicode) ->
            result = result.replace(latex, unicode)
        }
        
        // 3. 处理分数格式
        result = result.replace(Regex("""\\frac\{([^}]+)\}\{([^}]+)\}""")) { 
            "(${it.groupValues[1]})/(${it.groupValues[2]})" 
        }
        
        // 4. 处理上下标
        result = result.replace(Regex("""([a-zA-Z0-9])\^(\{[^}]+\}|\w)""")) { 
            "${it.groupValues[1]}^${it.groupValues[2].removeSurrounding("{", "}")}" 
        }
        result = result.replace(Regex("""([a-zA-Z0-9])_(\{[^}]+\}|\w)""")) { 
            "${it.groupValues[1]}_${it.groupValues[2].removeSurrounding("{", "}")}" 
        }
        
        return result
    }
    
    /**
     * 增强的数学内容识别 - 更准确地判断是否为数学表达式
     */
    fun isMathContent(text: String): Boolean {
        // 检查LaTeX命令
        val latexCommands = listOf(
            "\\frac", "\\sqrt", "\\sum", "\\int", "\\lim", "\\sin", "\\cos", "\\tan",
            "\\log", "\\ln", "\\exp", "\\alpha", "\\beta", "\\gamma", "\\delta",
            "\\epsilon", "\\theta", "\\lambda", "\\mu", "\\pi", "\\sigma", "\\phi", "\\omega"
        )
        
        if (latexCommands.any { text.contains(it) }) return true
        
        // 检查数学符号
        val mathSymbols = listOf("∑", "∫", "∞", "≤", "≥", "≠", "≈", "±", "×", "÷", "√")
        if (mathSymbols.any { text.contains(it) }) return true
        
        // 检查数学环境
        val mathEnvironments = listOf("\\begin{", "\\end{", "\\[", "\\]", "\\(", "\\)")
        if (mathEnvironments.any { text.contains(it) }) return true
        
        // 检查复杂的数学表达式模式
        val mathPatterns = listOf(
            Regex("""\w+\^\w+"""), // 上标
            Regex("""\w+_\w+"""), // 下标
            Regex("""\([^)]*[+\-*/=][^)]*\)"""), // 括号内的数学表达式
            Regex("""\d+/\d+"""), // 分数
            Regex("""\w+\s*[=<>≤≥≠]\s*\w+""") // 等式或不等式
        )
        
        return mathPatterns.any { it.containsMatchIn(text) }
    }
    
    /**
     * 将Markdown字符串转换为纯文本
     */
    fun markdownToPlainText(markdown: String): String {
        if (markdown.isEmpty()) return ""
        
        var result = markdown
        
        // 移除代码块
        result = result.replace(Regex("""```[\s\S]*?```"""), "")
        result = result.replace(Regex("""`[^`]*`"""), "")
        
        // 移除链接，保留文本
        result = result.replace(Regex("""\[([^\]]*)\]\([^)]*\)""")) { it.groupValues[1] }
        
        // 移除图片
        result = result.replace(Regex("""!\[([^\]]*)\]\([^)]*\)"""), "")
        
        // 移除标题标记
        result = result.replace(Regex("""^#{1,6}\s*""", RegexOption.MULTILINE), "")
        
        // 移除粗体和斜体标记
        result = result.replace(Regex("""\*\*([^*]*)\*\*""")) { it.groupValues[1] }
        result = result.replace(Regex("""\*([^*]*)\*""")) { it.groupValues[1] }
        result = result.replace(Regex("""__([^_]*)__""")) { it.groupValues[1] }
        result = result.replace(Regex("""_([^_]*)_""")) { it.groupValues[1] }
        
        // 移除删除线
        result = result.replace(Regex("""~~([^~]*)~~""")) { it.groupValues[1] }
        
        // 移除引用标记
        result = result.replace(Regex("""^>\s*""", RegexOption.MULTILINE), "")
        
        // 移除列表标记
        result = result.replace(Regex("""^[\s]*[-*+]\s+""", RegexOption.MULTILINE), "")
        result = result.replace(Regex("""^\s*\d+\.\s+""", RegexOption.MULTILINE), "")
        
        // 移除水平线
        result = result.replace(Regex("""^[-*_]{3,}$""", RegexOption.MULTILINE), "")
        
        // 清理多余的空行
        result = result.replace(Regex("""\n{3,}"""), "\n\n")
        
        return result.trim()
    }
}