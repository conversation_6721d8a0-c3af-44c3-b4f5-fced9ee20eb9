package com.example.everytalk.util

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp

/**
 * 简洁的Markdown渲染器 - 基于cherry-studio风格
 * 专注于核心功能，提供高效的Markdown和数学公式处理
 */
object SimpleMarkdownRenderer {
    
    // 样式定义
    private val headerStyle = SpanStyle(
        fontWeight = FontWeight.Bold,
        fontSize = 18.sp,
        color = Color(0xFF2D3748)
    )
    
    private val mathStyle = SpanStyle(
        fontFamily = FontFamily.Monospace,
        fontSize = 16.sp,
        color = Color(0xFF2B6CB0),
        background = Color(0xFFF7FAFC)
    )
    
    private val codeStyle = SpanStyle(
        fontFamily = FontFamily.Monospace,
        fontSize = 14.sp,
        color = Color(0xFFE53E3E),
        background = Color(0xFFF7FAFC)
    )
    
    private val boldStyle = SpanStyle(fontWeight = FontWeight.Bold)
    private val italicStyle = SpanStyle(fontStyle = FontStyle.Italic)
    
    private val linkStyle = SpanStyle(
        color = Color(0xFF3182CE),
        textDecoration = TextDecoration.Underline
    )
    
    private val listItemStyle = SpanStyle(color = Color(0xFF4A5568))
    private val quoteStyle = SpanStyle(
        fontStyle = FontStyle.Italic,
        color = Color(0xFF718096)
    )

    /**
     * 主要渲染函数 - 增强数学公式支持
     */
    fun renderText(
        text: String,
        isStreaming: Boolean = false,
        mathRenderConfig: MathFormulaRenderer.RenderConfig = MathFormulaRenderer.RenderConfig(
            mathEngine = MathFormulaRenderer.MathEngine.KATEX,
            enableInlineMath = true,
            enableDisplayMath = true
        )
    ): AnnotatedString {
        if (text.isBlank()) {
            return AnnotatedString("")
        }
        
        // 预处理文本 - 基于cherry-studio风格，增强数学公式处理
        val processedText = processMarkdownText(text, mathRenderConfig)
        
        // 使用增强的渲染逻辑
        return renderMarkdown(processedText, mathRenderConfig)
    }

    fun renderStreamingText(
        text: String,
        messageId: String,
        mathRenderConfig: MathFormulaRenderer.RenderConfig = MathFormulaRenderer.RenderConfig(
            mathEngine = MathFormulaRenderer.MathEngine.UNICODE,
            enableInlineMath = true,
            enableDisplayMath = true
        )
    ): AnnotatedString {
        return renderText(text, isStreaming = true, mathRenderConfig = mathRenderConfig)
    }
    
    /**
     * 预处理Markdown文本 - 增强数学公式处理
     */
    private fun processMarkdownText(
        text: String,
        mathConfig: MathFormulaRenderer.RenderConfig = MathFormulaRenderer.RenderConfig()
    ): String {
        var processed = text
        
        // 1. 标准化换行符
        processed = processed.replace(Regex("\\r\\n?"), "\n")
        
        // 2. 使用MathFormulaRenderer进行高级数学公式预处理
        if (processed.contains('$')) {
            processed = MathFormulaRenderer.preprocessMathContent(processed, mathConfig)
        }
        
        // 3. 处理LaTeX括号 - 基于cherry-studio的processLatexBrackets
        processed = processLatexBrackets(processed)
        
        // 4. 转换数学公式格式 - 基于cherry-studio的convertMathFormula
        processed = convertMathFormula(processed)
        
        // 5. 清理行尾空格
        processed = processed.replace(Regex("[ \\t]+$", RegexOption.MULTILINE), "")
        
        // 6. 标准化标题格式
        processed = processed.replace(Regex("^(#{1,6})([^\\s#])")) { matchResult ->
            "${matchResult.groupValues[1]} ${matchResult.groupValues[2]}"
        }
        
        // 7. 优化代码块和数学公式的分隔
        processed = optimizeContentSeparation(processed)
        
        return processed
    }
    
    /**
     * 优化内容分隔，确保代码块和数学公式不冲突
     */
    private fun optimizeContentSeparation(text: String): String {
        var result = text
        
        // 确保数学公式前后有适当间距，但不与其他格式冲突
        result = result.replace(Regex("([^\\s$])\\$\\$")) { "${it.groupValues[1]} $$" }
        result = result.replace(Regex("\\$\\$([^\\s$])")) { "$$ ${it.groupValues[1]}" }
        
        // 确保行内数学公式的合理间距
        result = result.replace(Regex("([\\u4e00-\\u9fa5a-zA-Z])\\$([^$]+)\\$([\\u4e00-\\u9fa5a-zA-Z])")) {
            "${it.groupValues[1]} $${it.groupValues[2]}$ ${it.groupValues[3]}"
        }
        
        return result
    }
    
    /**
     * 处理LaTeX括号 - 基于cherry-studio实现
     */
    private fun processLatexBrackets(text: String): String {
        var result = text
        
        // 处理 \[ \] 块级数学公式
        result = result.replace(Regex("\\\\\\[([\\s\\S]*?)\\\\\\]")) { match ->
            val content = match.groupValues[1].trim()
            "$$$$content$$"
        }
        
        // 处理 \( \) 行内数学公式
        result = result.replace(Regex("\\\\\\(([\\s\\S]*?)\\\\\\)")) { match ->
            val content = match.groupValues[1].trim()
            "$$content$$"
        }
        
        return result
    }
    
    /**
     * 转换数学公式格式 - 基于cherry-studio实现
     */
    private fun convertMathFormula(text: String): String {
        var result = text
        
        try {
            // 确保数学公式周围有适当的空格 - 使用安全的替换方式
            result = result.replace(Regex("([^\\s$])\\$\\$")) { matchResult ->
                "${matchResult.groupValues[1]} $$"
            }
            result = result.replace(Regex("\\$\\$([^\\s$])")) { matchResult ->
                "$$ ${matchResult.groupValues[1]}"
            }
            result = result.replace(Regex("([^\\s$])\\$")) { matchResult ->
                "${matchResult.groupValues[1]} $"
            }
            result = result.replace(Regex("\\$([^\\s$])")) { matchResult ->
                "$ ${matchResult.groupValues[1]}"
            }
        } catch (e: Exception) {
            // 如果正则表达式处理失败，返回原始文本
            return text
        }
        
        return result
    }
    
    /**
     * 增强的Markdown渲染 - 基于cherry-studio风格，优化数学公式显示
     */
    private fun renderMarkdown(
        text: String,
        mathConfig: MathFormulaRenderer.RenderConfig = MathFormulaRenderer.RenderConfig()
    ): AnnotatedString {
        return buildAnnotatedString {
            var currentIndex = 0
            
            // 简化的模式匹配 - 只处理核心Markdown元素
            val patterns = listOf(
                // 数学公式 - 最高优先级
                "MATH_BLOCK" to Regex("\\$\\$([\\s\\S]*?)\\$\\$"),
                "MATH_INLINE" to Regex("\\$([^$\\n\r]*?)\\$"),
                // 代码
                "CODE_BLOCK" to Regex("```[\\w]*\\n?([\\s\\S]*?)```"),
                "CODE_INLINE" to Regex("`([^`]*?)`"),
                // 标题
                "HEADER" to Regex("^(#{1,6})\\s*(.+)$", RegexOption.MULTILINE),
                // 文本格式
                "BOLD" to Regex("\\*\\*([^*]*?)\\*\\*"),
                "ITALIC" to Regex("(?<!\\*)\\*([^*\\n]*?)\\*(?!\\*)"),
                // 列表
                "LIST" to Regex("^[\\s]*[-*+]\\s+(.+)$", RegexOption.MULTILINE),
                // 链接
                "LINK" to Regex("\\[([^\\]]*?)\\]\\(([^)]*?)\\)"),
                // 引用
                "QUOTE" to Regex("^>\\s*(.+)$", RegexOption.MULTILINE)
            )
            
            while (currentIndex < text.length) {
                var earliestMatch: Triple<String, MatchResult, Int>? = null
                
                // 找到最早的匹配
                for ((type, regex) in patterns) {
                    val match = regex.find(text, currentIndex)
                    if (match != null) {
                        if (earliestMatch == null || match.range.first < earliestMatch.third) {
                            earliestMatch = Triple(type, match, match.range.first)
                        }
                    }
                }
                
                if (earliestMatch != null) {
                    val (type, match, startIndex) = earliestMatch
                    
                    // 添加匹配前的普通文本
                    if (startIndex > currentIndex) {
                        append(text.substring(currentIndex, startIndex))
                    }
                    
                    // 处理匹配的内容
                    when (type) {
                        "HEADER" -> {
                            val headerText = match.groupValues[2].trim()
                            withStyle(headerStyle) {
                                append(headerText)
                            }
                        }
                        "MATH_BLOCK", "MATH_INLINE" -> {
                            val mathContent = match.groupValues[1].trim()
                            if (mathContent.isNotEmpty()) {
                                withStyle(mathStyle) {
                                    // 使用改进的数学公式处理
                                    val processedMath = processImprovedMath(mathContent, type == "MATH_BLOCK")
                                    append(processedMath)
                                }
                            }
                        }
                        "CODE_BLOCK" -> {
                            val codeContent = match.groupValues[1].trim()
                            if (codeContent.isNotEmpty()) {
                                withStyle(codeStyle) {
                                    append(codeContent)
                                }
                            }
                        }
                        "CODE_INLINE" -> {
                            val codeContent = match.groupValues[1].trim()
                            if (codeContent.isNotEmpty()) {
                                withStyle(codeStyle) {
                                    append(codeContent)
                                }
                            }
                        }
                        "BOLD" -> {
                            withStyle(boldStyle) {
                                append(match.groupValues[1])
                            }
                        }
                        "ITALIC" -> {
                            withStyle(italicStyle) {
                                append(match.groupValues[1])
                            }
                        }
                        "LIST" -> {
                            val listContent = match.groupValues[1].trim()
                            withStyle(listItemStyle) {
                                append("• $listContent")
                            }
                        }
                        "LINK" -> {
                            val linkText = match.groupValues[1]
                            val linkUrl = match.groupValues[2]
                            pushStringAnnotation("URL", linkUrl)
                            withStyle(linkStyle) {
                                append(linkText)
                            }
                            pop()
                        }
                        "QUOTE" -> {
                            val quoteContent = match.groupValues[1].trim()
                            withStyle(quoteStyle) {
                                append("❝ $quoteContent")
                            }
                        }
                    }
                    
                    currentIndex = match.range.last + 1
                } else {
                    // 没有更多匹配，添加剩余文本
                    append(text.substring(currentIndex))
                    break
                }
            }
        }
    }
    
    /**
     * 深度清理文本 - 保留基本功能
     */
    fun deepCleanText(text: String): String {
        var cleaned = text
        
        // 1. 标准化换行符
        cleaned = cleaned.replace(Regex("\\r\\n?"), "\n")
        
        // 2. 移除表格相关符号
        cleaned = cleaned.replace(Regex("\\|\\s*:?\\s*-+\\s*:?\\s*\\|"), "")
        cleaned = cleaned.replace(Regex("^\\s*\\|.*\\|\\s*$", RegexOption.MULTILINE), "")
        cleaned = cleaned.replace(Regex("\\s*\\|\\s*"), " ")
        
        // 3. 移除水平分割线
        cleaned = cleaned.replace(Regex("^\\s*[-=_*]{3,}\\s*$", RegexOption.MULTILINE), "")
        
        // 4. 清理重复符号
        cleaned = cleaned.replace(Regex("[:]{2,}"), ":")
        cleaned = cleaned.replace(Regex("[|]{2,}"), "")
        
        // 5. 优化空行
        cleaned = cleaned.replace(Regex("\\n{3,}"), "\n\n")
        
        return cleaned.trim()
    }
    
    /**
     * 改进的数学公式处理 - 专为Android Compose优化
     */
    private fun processImprovedMath(mathContent: String, isDisplayMath: Boolean): String {
        // 使用全新的数学公式转换器
        val converted = convertMathToReadableText(mathContent)
        return if (isDisplayMath) {
            "\n📐 $converted\n"
        } else {
            converted
        }
    }
    
    /**
     * 将LaTeX数学公式转换为可读的文本格式
     */
    private fun convertMathToReadableText(latex: String): String {
        var result = latex.trim()
        
        // 移除外层的$符号
        result = result.removePrefix("$").removeSuffix("$")
        result = result.removePrefix("$").removeSuffix("$")
        
        // 1. 处理复杂结构（按优先级处理）
        
        // 处理分数 \frac{a}{b} -> (a)/(b)
        result = result.replace(Regex("""\\frac\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}""")) { match ->
            val numerator = convertMathToReadableText(match.groupValues[1])
            val denominator = convertMathToReadableText(match.groupValues[2])
            "($numerator)/($denominator)"
        }
        
        // 处理根号 \sqrt{x} -> √(x)
        result = result.replace(Regex("""\\sqrt\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}""")) { match ->
            val content = convertMathToReadableText(match.groupValues[1])
            "√($content)"
        }
        
        // 处理n次根号 \sqrt[n]{x} -> ⁿ√(x)
        result = result.replace(Regex("""\\sqrt\s*\[([^\]]+)\]\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}""")) { match ->
            val index = convertToSuperscript(match.groupValues[1])
            val content = convertMathToReadableText(match.groupValues[2])
            "${index}√($content)"
        }
        
        // 处理上标 x^{...} 或 x^a
        result = result.replace(Regex("""([a-zA-Z0-9αβγδεζηθικλμνξπρστυφχψωΓΔΘΛΞΠΣΥΦΨΩ√∑∏∫]+)\^(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}|[a-zA-Z0-9])""")) { match ->
            val base = match.groupValues[1]
            val exponent = match.groupValues[2].removeSurrounding("{", "}")
            val convertedExponent = if (exponent.length == 1) {
                convertToSuperscript(exponent)
            } else {
                "^($exponent)"
            }
            "$base$convertedExponent"
        }
        
        // 处理下标 x_{...} 或 x_a
        result = result.replace(Regex("""([a-zA-Z0-9αβγδεζηθικλμνξπρστυφχψωΓΔΘΛΞΠΣΥΦΨΩ√∑∏∫]+)_(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}|[a-zA-Z0-9])""")) { match ->
            val base = match.groupValues[1]
            val subscript = match.groupValues[2].removeSurrounding("{", "}")
            val convertedSubscript = if (subscript.length == 1) {
                convertToSubscript(subscript)
            } else {
                "_($subscript)"
            }
            "$base$convertedSubscript"
        }
        
        // 2. 处理数学函数和运算符
        val mathFunctions = mapOf(
            // 三角函数
            "\\sin" to "sin", "\\cos" to "cos", "\\tan" to "tan",
            "\\sec" to "sec", "\\csc" to "csc", "\\cot" to "cot",
            "\\arcsin" to "arcsin", "\\arccos" to "arccos", "\\arctan" to "arctan",
            "\\sinh" to "sinh", "\\cosh" to "cosh", "\\tanh" to "tanh",
            
            // 对数函数
            "\\log" to "log", "\\ln" to "ln", "\\lg" to "lg",
            
            // 其他函数
            "\\exp" to "exp", "\\max" to "max", "\\min" to "min",
            "\\lim" to "lim", "\\sup" to "sup", "\\inf" to "inf"
        )
        
        mathFunctions.forEach { (latex, readable) ->
            result = result.replace(latex, readable)
        }
        
        // 3. 处理希腊字母
        val greekLetters = mapOf(
            "\\alpha" to "α", "\\beta" to "β", "\\gamma" to "γ", "\\delta" to "δ",
            "\\epsilon" to "ε", "\\varepsilon" to "ε", "\\zeta" to "ζ", "\\eta" to "η", 
            "\\theta" to "θ", "\\vartheta" to "θ", "\\iota" to "ι", "\\kappa" to "κ",
            "\\lambda" to "λ", "\\mu" to "μ", "\\nu" to "ν", "\\xi" to "ξ",
            "\\pi" to "π", "\\varpi" to "π", "\\rho" to "ρ", "\\varrho" to "ρ",
            "\\sigma" to "σ", "\\varsigma" to "ς", "\\tau" to "τ", "\\upsilon" to "υ",
            "\\phi" to "φ", "\\varphi" to "φ", "\\chi" to "χ", "\\psi" to "ψ", "\\omega" to "ω",
            
            // 大写希腊字母
            "\\Gamma" to "Γ", "\\Delta" to "Δ", "\\Theta" to "Θ", "\\Lambda" to "Λ",
            "\\Xi" to "Ξ", "\\Pi" to "Π", "\\Sigma" to "Σ", "\\Upsilon" to "Υ",
            "\\Phi" to "Φ", "\\Psi" to "Ψ", "\\Omega" to "Ω"
        )
        
        greekLetters.forEach { (latex, unicode) ->
            result = result.replace(latex, unicode)
        }
        
        // 4. 处理数学运算符
        val mathOperators = mapOf(
            "\\pm" to "±", "\\mp" to "∓", "\\times" to "×", "\\div" to "÷",
            "\\cdot" to "·", "\\ast" to "*", "\\star" to "⋆", "\\circ" to "∘",
            "\\bullet" to "•", "\\cap" to "∩", "\\cup" to "∪", "\\vee" to "∨",
            "\\wedge" to "∧", "\\oplus" to "⊕", "\\ominus" to "⊖", "\\otimes" to "⊗",
            "\\odot" to "⊙", "\\oslash" to "⊘", "\\bigcap" to "⋂", "\\bigcup" to "⋃"
        )
        
        mathOperators.forEach { (latex, unicode) ->
            result = result.replace(latex, unicode)
        }
        
        // 5. 处理关系符号
        val relationSymbols = mapOf(
            "\\leq" to "≤", "\\le" to "≤", "\\geq" to "≥", "\\ge" to "≥",
            "\\neq" to "≠", "\\ne" to "≠", "\\approx" to "≈", "\\equiv" to "≡",
            "\\sim" to "∼", "\\simeq" to "≃", "\\cong" to "≅", "\\propto" to "∝",
            "\\parallel" to "∥", "\\perp" to "⊥", "\\ll" to "≪", "\\gg" to "≫"
        )
        
        relationSymbols.forEach { (latex, unicode) ->
            result = result.replace(latex, unicode)
        }
        
        // 6. 处理集合符号
        val setSymbols = mapOf(
            "\\in" to "∈", "\\notin" to "∉", "\\ni" to "∋", "\\notni" to "∌",
            "\\subset" to "⊂", "\\supset" to "⊃", "\\subseteq" to "⊆", "\\supseteq" to "⊇",
            "\\subsetneq" to "⊊", "\\supsetneq" to "⊋", "\\emptyset" to "∅", "\\varnothing" to "∅"
        )
        
        setSymbols.forEach { (latex, unicode) ->
            result = result.replace(latex, unicode)
        }
        
        // 7. 处理箭头
        val arrows = mapOf(
            "\\leftarrow" to "←", "\\gets" to "←", "\\rightarrow" to "→", "\\to" to "→",
            "\\leftrightarrow" to "↔", "\\Leftarrow" to "⇐", "\\Rightarrow" to "⇒",
            "\\Leftrightarrow" to "⇔", "\\mapsto" to "↦", "\\longmapsto" to "⟼",
            "\\uparrow" to "↑", "\\downarrow" to "↓", "\\updownarrow" to "↕"
        )
        
        arrows.forEach { (latex, unicode) ->
            result = result.replace(latex, unicode)
        }
        
        // 8. 处理微积分和求和符号
        val calcSymbols = mapOf(
            "\\partial" to "∂", "\\nabla" to "∇", "\\infty" to "∞",
            "\\sum" to "∑", "\\prod" to "∏", "\\int" to "∫",
            "\\iint" to "∬", "\\iiint" to "∭", "\\oint" to "∮"
        )
        
        calcSymbols.forEach { (latex, unicode) ->
            result = result.replace(latex, unicode)
        }
        
        // 9. 处理其他常用符号
        val otherSymbols = mapOf(
            "\\degree" to "°", "\\prime" to "′", "\\dprime" to "″",
            "\\angle" to "∠", "\\triangle" to "△", "\\square" to "□",
            "\\diamond" to "◊", "\\clubsuit" to "♣", "\\diamondsuit" to "♦",
            "\\heartsuit" to "♥", "\\spadesuit" to "♠"
        )
        
        otherSymbols.forEach { (latex, unicode) ->
            result = result.replace(latex, unicode)
        }
        
        // 10. 清理剩余的LaTeX命令（转换为可读形式）
        result = result.replace(Regex("""\\([a-zA-Z]+)""")) { match ->
            val command = match.groupValues[1]
            when (command) {
                "left", "right" -> "" // 移除括号修饰符
                "big", "Big", "bigg", "Bigg" -> "" // 移除大小修饰符
                else -> command // 保留未知命令的名称
            }
        }
        
        // 11. 清理多余的空格和符号
        result = result.replace(Regex("\\s+"), " ")
        result = result.replace(Regex("\\{|\\}"), "")
        result = result.trim()
        
        return result
    }
    
    /**
     * 转换为上标字符
     */
    private fun convertToSuperscript(text: String): String {
        val superscriptMap = mapOf(
            '0' to '⁰', '1' to '¹', '2' to '²', '3' to '³', '4' to '⁴',
            '5' to '⁵', '6' to '⁶', '7' to '⁷', '8' to '⁸', '9' to '⁹',
            '+' to '⁺', '-' to '⁻', '=' to '⁼', '(' to '⁽', ')' to '⁾',
            'a' to 'ᵃ', 'b' to 'ᵇ', 'c' to 'ᶜ', 'd' to 'ᵈ', 'e' to 'ᵉ',
            'f' to 'ᶠ', 'g' to 'ᵍ', 'h' to 'ʰ', 'i' to 'ⁱ', 'j' to 'ʲ',
            'k' to 'ᵏ', 'l' to 'ˡ', 'm' to 'ᵐ', 'n' to 'ⁿ', 'o' to 'ᵒ',
            'p' to 'ᵖ', 'r' to 'ʳ', 's' to 'ˢ', 't' to 'ᵗ', 'u' to 'ᵘ',
            'v' to 'ᵛ', 'w' to 'ʷ', 'x' to 'ˣ', 'y' to 'ʸ', 'z' to 'ᶻ'
        )
        return text.map { superscriptMap[it] ?: it }.joinToString("")
    }
    
    /**
     * 转换为下标字符
     */
    private fun convertToSubscript(text: String): String {
        val subscriptMap = mapOf(
            '0' to '₀', '1' to '₁', '2' to '₂', '3' to '₃', '4' to '₄',
            '5' to '₅', '6' to '₆', '7' to '₇', '8' to '₈', '9' to '₉',
            '+' to '₊', '-' to '₋', '=' to '₌', '(' to '₍', ')' to '₎',
            'a' to 'ₐ', 'e' to 'ₑ', 'h' to 'ₕ', 'i' to 'ᵢ', 'j' to 'ⱼ',
            'k' to 'ₖ', 'l' to 'ₗ', 'm' to 'ₘ', 'n' to 'ₙ', 'o' to 'ₒ',
            'p' to 'ₚ', 'r' to 'ᵣ', 's' to 'ₛ', 't' to 'ₜ', 'u' to 'ᵤ',
            'v' to 'ᵥ', 'x' to 'ₓ'
        )
        return text.map { subscriptMap[it] ?: it }.joinToString("")
    }

    /**
     * 高级数学公式内容处理
     */
    private fun processMathContentAdvanced(
        mathContent: String,
        isDisplayMath: Boolean,
        config: MathFormulaRenderer.RenderConfig
    ): String {
        return when (config.mathEngine) {
            MathFormulaRenderer.MathEngine.UNICODE -> {
                // 使用Unicode转换，添加显示数学公式的特殊处理
                val converted = MathFormulaRenderer.convertLatexToUnicode(mathContent)
                if (isDisplayMath) {
                    "\n$converted\n"
                } else {
                    converted
                }
            }
            MathFormulaRenderer.MathEngine.KATEX -> {
                // 为KaTeX优化LaTeX语法
                val optimized = optimizeLatexForKaTeX(mathContent)
                if (isDisplayMath) {
                    "\n$optimized\n"
                } else {
                    optimized
                }
            }
            MathFormulaRenderer.MathEngine.MATHJAX -> {
                // MathJax兼容处理
                val processed = optimizeLatexForMathJax(mathContent)
                if (isDisplayMath) {
                    "\n$processed\n"
                } else {
                    processed
                }
            }
            else -> {
                // 默认使用Unicode转换
                MathFormulaRenderer.convertLatexToUnicode(mathContent)
            }
        }
    }
    
    /**
     * 为KaTeX优化LaTeX语法
     */
    private fun optimizeLatexForKaTeX(latex: String): String {
        var optimized = latex
        
        // KaTeX特定的优化
        val katexOptimizations = mapOf(
            "\\begin{align}" to "\\begin{aligned}",
            "\\end{align}" to "\\end{aligned}",
            "\\begin{align*}" to "\\begin{aligned}",
            "\\end{align*}" to "\\end{aligned}",
            "\\displaystyle" to "",  // KaTeX自动处理
        )
        
        katexOptimizations.forEach { (pattern, replacement) ->
            optimized = optimized.replace(pattern, replacement)
        }
        
        return optimized.trim()
    }
    
    /**
     * 为MathJax优化LaTeX语法
     */
    private fun optimizeLatexForMathJax(latex: String): String {
        var optimized = latex
        
        // MathJax特定的优化
        // MathJax支持更多LaTeX命令，主要是格式清理
        optimized = optimized.replace(Regex("\\s+"), " ").trim()
        
        return optimized
    }
    
    /**
     * 创建智能数学公式渲染配置
     */
    fun createSmartMathConfig(contentComplexity: Float = 0.5f): MathFormulaRenderer.RenderConfig {
        return when {
            contentComplexity > 0.8f -> MathFormulaRenderer.RenderConfig(
                mathEngine = MathFormulaRenderer.MathEngine.MATHJAX,
                enableInlineMath = true,
                enableDisplayMath = true
            )
            contentComplexity > 0.4f -> MathFormulaRenderer.RenderConfig(
                mathEngine = MathFormulaRenderer.MathEngine.KATEX,
                enableInlineMath = true,
                enableDisplayMath = true
            )
            else -> MathFormulaRenderer.RenderConfig(
                mathEngine = MathFormulaRenderer.MathEngine.UNICODE,
                enableInlineMath = true,
                enableDisplayMath = true
            )
        }
    }
    
    /**
     * 评估内容复杂度（用于选择合适的数学渲染引擎）
     */
    fun assessContentComplexity(text: String): Float {
        var complexity = 0.0f
        
        // 基于数学公式数量
        val mathFormulaCount = text.count { it == '$' } / 2
        complexity += mathFormulaCount * 0.1f
        
        // 基于LaTeX命令复杂度
        val complexCommands = listOf("\\frac", "\\sqrt", "\\sum", "\\int", "\\matrix", "\\begin")
        complexCommands.forEach { command ->
            complexity += text.split(command).size * 0.05f
        }
        
        // 基于内容长度
        complexity += (text.length / 1000.0f) * 0.1f
        
        return minOf(complexity, 1.0f)
    }
}