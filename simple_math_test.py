#!/usr/bin/env python3
"""
简单的数学公式优化测试
验证核心功能
"""

import sys
import os

# 添加后端路径到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend-docker', 'eztalk_proxy'))

from utils.math_formula_converter import MathFormulaConverter

def test_core_features():
    """测试核心功能"""
    print("=== 数学公式优化核心功能测试 ===")
    
    converter = MathFormulaConverter()
    
    test_cases = [
        # 基本的泰勒级数
        ("指数函数泰勒级数", "$e^x = 1 + x + \\frac{x^2}{2!} + \\frac{x^3}{3!} + ...$"),
        
        # 三角函数
        ("三角函数", "$\\sin(x) + \\cos(x) + \\tan(x)$"),
        
        # 阶乘
        ("阶乘表达式", "$n! = n \\times (n-1)!$"),
        
        # 导数
        ("导数表达式", "$f'(x) = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h}$"),
        
        # 分数和上下标
        ("分数和指数", "$\\frac{x^2 + 1}{x - 1} = x + 2 + \\frac{3}{x - 1}$"),
        
        # 求和
        ("求和表达式", "$\\sum_{n=0}^{\\infty} \\frac{x^n}{n!}$"),
    ]
    
    for name, formula in test_cases:
        print(f"\n{name}:")
        print(f"输入: {formula}")
        result = converter.enhance_math_formulas(formula)
        print(f"输出: {result}")
        print("-" * 40)

def test_image_formulas():
    """测试图片中的具体公式"""
    print("\n=== 图片中的数学公式测试 ===")
    
    converter = MathFormulaConverter()
    
    # 图片中的指数函数泰勒级数
    formula1 = """$$e^x = \\sum_{n=0}^{\\infty} \\frac{x^n}{n!} = 1 + x + \\frac{x^2}{2!} + \\frac{x^3}{3!} + \\frac{x^4}{4!} + ...$$"""
    
    # 图片中的三角函数泰勒级数
    formula2 = """$$\\sin(x) = \\sum_{n=0}^{\\infty} \\frac{(-1)^n x^{2n+1}}{(2n+1)!} = x - \\frac{x^3}{3!} + \\frac{x^5}{5!} - ...$$"""
    
    formula3 = """$$\\cos(x) = \\sum_{n=0}^{\\infty} \\frac{(-1)^n x^{2n}}{(2n)!} = 1 - \\frac{x^2}{2!} + \\frac{x^4}{4!} - ...$$"""
    
    formulas = [
        ("指数函数 e^x 的泰勒级数", formula1),
        ("正弦函数 sin(x) 的泰勒级数", formula2),
        ("余弦函数 cos(x) 的泰勒级数", formula3),
    ]
    
    for name, formula in formulas:
        print(f"\n{name}:")
        print(f"原始: {formula}")
        result = converter.enhance_math_formulas(formula)
        print(f"优化: {result}")
        print("-" * 50)

def main():
    """主测试函数"""
    print("数学公式优化简单测试")
    print("=" * 60)
    
    try:
        test_core_features()
        test_image_formulas()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("\n优化功能包括：")
        print("1. ✅ 泰勒级数和幂级数格式化")
        print("2. ✅ 阶乘表示法优化")
        print("3. ✅ 数学函数格式化（三角函数、对数、指数等）")
        print("4. ✅ 导数和微分符号处理")
        print("5. ✅ 上下标和分数格式化")
        print("6. ✅ 求和和积分符号处理")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
