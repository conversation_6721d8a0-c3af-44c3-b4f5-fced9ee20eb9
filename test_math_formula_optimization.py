#!/usr/bin/env python3
"""
测试数学公式优化功能
验证泰勒级数、阶乘、导数等高级数学表达式的处理
"""

import sys
import os

# 添加后端路径到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend-docker', 'eztalk_proxy'))

from utils.math_formula_converter import MathFormulaConverter

def test_taylor_series():
    """测试泰勒级数处理"""
    print("=== 测试泰勒级数处理 ===")
    
    converter = MathFormulaConverter()
    
    test_cases = [
        # 指数函数的泰勒级数
        "$f(x) = e^x = \\sum_{n=0}^{\\infty} \\frac{x^n}{n!} = 1 + x + \\frac{x^2}{2!} + \\frac{x^3}{3!} + ...$",
        
        # 正弦函数的泰勒级数
        "$\\sin(x) = \\sum_{n=0}^{\\infty} \\frac{(-1)^n x^{2n+1}}{(2n+1)!} = x - \\frac{x^3}{3!} + \\frac{x^5}{5!} - ...$",
        
        # 余弦函数的泰勒级数
        "$\\cos(x) = \\sum_{n=0}^{\\infty} \\frac{(-1)^n x^{2n}}{(2n)!} = 1 - \\frac{x^2}{2!} + \\frac{x^4}{4!} - ...$",
        
        # n次导数表示法
        "$f^{(n)}(0) = n!$ 当 $f(x) = x^n$ 时",
        
        # 阶乘表达式
        "$n! = n \\times (n-1) \\times ... \\times 2 \\times 1$",
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {test_case}")
        result = converter.enhance_math_formulas(test_case)
        print(f"输出: {result}")

def test_derivatives():
    """测试导数处理"""
    print("\n=== 测试导数处理 ===")
    
    converter = MathFormulaConverter()
    
    test_cases = [
        # 一阶导数
        "$f'(x) = \\frac{d}{dx}f(x)$",
        
        # 高阶导数
        "$f^{(n)}(x) = \\frac{d^n}{dx^n}f(x)$",
        
        # 偏导数
        "$\\frac{\\partial f}{\\partial x} = \\lim_{h \\to 0} \\frac{f(x+h,y) - f(x,y)}{h}$",
        
        # 全微分
        "$df = \\frac{\\partial f}{\\partial x}dx + \\frac{\\partial f}{\\partial y}dy$",
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {test_case}")
        result = converter.enhance_math_formulas(test_case)
        print(f"输出: {result}")

def test_math_functions():
    """测试数学函数处理"""
    print("\n=== 测试数学函数处理 ===")
    
    converter = MathFormulaConverter()
    
    test_cases = [
        # 三角函数
        "$\\sin(x), \\cos(x), \\tan(x)$",
        
        # 反三角函数
        "$\\arcsin(x), \\arccos(x), \\arctan(x)$",
        
        # 双曲函数
        "$\\sinh(x), \\cosh(x), \\tanh(x)$",
        
        # 对数和指数函数
        "$\\ln(x), \\log(x), \\exp(x)$",
        
        # 复合函数
        "$\\sin(\\cos(x)) + \\ln(\\exp(x^2))$",
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {test_case}")
        result = converter.enhance_math_formulas(test_case)
        print(f"输出: {result}")

def test_chinese_math_expressions():
    """测试中文数学表达式处理"""
    print("\n=== 测试中文数学表达式处理 ===")
    
    converter = MathFormulaConverter()
    
    test_cases = [
        # 中文数学术语
        "设 $f(x) = e^x$，求 $f'(x)$",
        
        # 中文描述的数学关系
        "令 $A$ 为 $n \\times n$ 的实可逆矩阵",
        
        # 混合中英文数学表达式
        "因此 $\\lim_{x \\to 0} \\frac{\\sin(x)}{x} = 1$",
        
        # 中文数学运算
        "所以 $a + b = c$，其中 $a > 0$",
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {test_case}")
        result = converter.enhance_chinese_math_expressions(test_case)
        print(f"输出: {result}")

def test_complex_expressions():
    """测试复杂数学表达式"""
    print("\n=== 测试复杂数学表达式 ===")
    
    converter = MathFormulaConverter()
    
    test_cases = [
        # 图片中的指数函数泰勒级数
        """设 $f(x) = e^x$，则：
        $f(0) = e^0 = 1$
        $f'(x) = e^x$，所以 $f'(0) = 1$
        $f''(x) = e^x$，所以 $f''(0) = 1$
        一般地，$f^{(n)}(0) = 1$
        
        因此泰勒级数为：
        $$e^x = \\sum_{n=0}^{\\infty} \\frac{f^{(n)}(0)}{n!}x^n = \\sum_{n=0}^{\\infty} \\frac{x^n}{n!}$$""",
        
        # 三角函数的泰勒级数
        """正弦函数的泰勒级数：
        $$\\sin(x) = x - \\frac{x^3}{3!} + \\frac{x^5}{5!} - \\frac{x^7}{7!} + \\cdots$$
        
        余弦函数的泰勒级数：
        $$\\cos(x) = 1 - \\frac{x^2}{2!} + \\frac{x^4}{4!} - \\frac{x^6}{6!} + \\cdots$$""",
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n复杂表达式测试 {i}:")
        print(f"输入:\n{test_case}")
        result = converter.enhance_math_formulas(test_case)
        print(f"输出:\n{result}")
        print("-" * 50)

def main():
    """主测试函数"""
    print("数学公式优化测试")
    print("=" * 50)
    
    try:
        test_taylor_series()
        test_derivatives()
        test_math_functions()
        test_chinese_math_expressions()
        test_complex_expressions()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
