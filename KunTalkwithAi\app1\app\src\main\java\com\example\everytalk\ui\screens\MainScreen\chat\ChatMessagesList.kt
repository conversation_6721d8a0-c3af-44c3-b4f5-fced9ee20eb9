@file:OptIn(ExperimentalFoundationApi::class)
package com.example.everytalk.ui.screens.MainScreen.chat

import android.widget.Toast
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.scrollBy
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.*
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.example.everytalk.R
import com.example.everytalk.data.DataClass.Message
import com.example.everytalk.statecontroller.AppViewModel
import com.example.everytalk.ui.screens.BubbleMain.Main.AttachmentsContent
import com.example.everytalk.ui.screens.BubbleMain.Main.ReasoningToggleAndContent
import com.example.everytalk.ui.screens.BubbleMain.Main.UserOrErrorMessageContent
import com.example.everytalk.ui.theme.ChatDimensions
import com.example.everytalk.ui.theme.chatColors
import com.example.everytalk.util.MarkdownBlock
import com.example.everytalk.util.SimpleMarkdownRenderer
import com.example.everytalk.ui.components.CodePreviewButton
import kotlinx.coroutines.launch

private val LocalOnTextLayout = compositionLocalOf<((androidx.compose.ui.text.TextLayoutResult, AnnotatedString) -> Unit)?> { null }

@Composable
fun ChatMessagesList(
    chatItems: List<ChatListItem>,
    viewModel: AppViewModel,
    listState: LazyListState,
    scrollStateManager: ChatScrollStateManager,
    bubbleMaxWidth: Dp,
    onShowAiMessageOptions: (Message) -> Unit,
    onImageLoaded: () -> Unit
) {
    val haptic = LocalHapticFeedback.current
    val coroutineScope = rememberCoroutineScope()
    val animatedItems = remember { mutableStateMapOf<String, Boolean>() }
    val isApiCalling by viewModel.isApiCalling.collectAsState()
    val renderer = remember { SimpleMarkdownRenderer }

    LaunchedEffect(chatItems) {
        if (chatItems.lastOrNull() is ChatListItem.AiMessageReasoning) {
            scrollStateManager.jumpToBottom()
        }
    }

    LazyColumn(
        state = listState,
        modifier = Modifier
            .fillMaxSize()
            .nestedScroll(scrollStateManager.nestedScrollConnection),
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        itemsIndexed(
            items = chatItems,
            key = { _, item -> item.stableId },
            contentType = { _, item -> item::class.java.simpleName }
        ) { index, item ->
            val alpha = remember { Animatable(0f) }
            val translationY = remember { Animatable(50f) }

            LaunchedEffect(item.stableId) {
                if (animatedItems[item.stableId] != true) {
                    launch {
                        alpha.animateTo(1f, animationSpec = tween(durationMillis = 300))
                    }
                    launch {
                        translationY.animateTo(0f, animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing))
                    }
                    animatedItems[item.stableId] = true
                } else {
                    alpha.snapTo(1f)
                    translationY.snapTo(0f)
                }
            }

            Box(
                modifier = Modifier
                    .graphicsLayer {
                        this.alpha = alpha.value
                        this.translationY = translationY.value
                    }
            ) {
                when (item) {
                    is ChatListItem.UserMessage -> {
                        val message = viewModel.getMessageById(item.messageId)
                        if (message != null) {
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalAlignment = Alignment.End
                            ) {
                                if (!item.attachments.isNullOrEmpty()) {
                                    AttachmentsContent(
                                        attachments = item.attachments,
                                        onAttachmentClick = { },
                                        maxWidth = bubbleMaxWidth * 0.8f,
                                        message = message,
                                        onEditRequest = { viewModel.requestEditMessage(it) },
                                        onRegenerateRequest = {
                                            viewModel.regenerateAiResponse(it)
                                            scrollStateManager.jumpToBottom()
                                        },
                                        scrollStateManager = scrollStateManager,
                                        onImageLoaded = onImageLoaded,
                                    )
                                }
                                if (item.text.isNotBlank()) {
                                    UserOrErrorMessageContent(
                                        message = message,
                                        displayedText = item.text,
                                        showLoadingDots = false,
                                        bubbleColor = MaterialTheme.chatColors.userBubble,
                                        contentColor = MaterialTheme.colorScheme.onSurface,
                                        isError = false,
                                        maxWidth = bubbleMaxWidth * 0.8f,
                                        onEditRequest = { viewModel.requestEditMessage(it) },
                                        onRegenerateRequest = {
                                            viewModel.regenerateAiResponse(it)
                                            scrollStateManager.jumpToBottom()
                                        },
                                        scrollStateManager = scrollStateManager,
                                    )
                                }
                            }
                        }
                    }

                    is ChatListItem.AiMessageReasoning -> {
                        val reasoningCompleteMap = viewModel.reasoningCompleteMap
                        val isReasoningStreaming = remember(isApiCalling, item.message.reasoning, reasoningCompleteMap[item.message.id]) {
                            isApiCalling && item.message.reasoning != null && reasoningCompleteMap[item.message.id] != true
                        }
                        val isReasoningComplete = reasoningCompleteMap[item.message.id] ?: false

                        ReasoningToggleAndContent(
                            modifier = Modifier.fillMaxWidth(),
                            currentMessageId = item.message.id,
                            displayedReasoningText = item.message.reasoning ?: "",
                            isReasoningStreaming = isReasoningStreaming,
                            isReasoningComplete = isReasoningComplete,
                            messageIsError = item.message.isError,
                            mainContentHasStarted = item.message.contentStarted,
                            reasoningTextColor = MaterialTheme.chatColors.reasoningText,
                            reasoningToggleDotColor = MaterialTheme.colorScheme.onSurface,
                            onVisibilityChanged = { }
                        )
                    }

                    is ChatListItem.AiMessageBlock -> {
                        var lastHeight by remember { mutableStateOf(0) }
                        val isCodeBlock = item.block is MarkdownBlock.CodeBlock
                        
                        Column {
                            AiMessageBlockItem(
                                item = item,
                                maxWidth = bubbleMaxWidth,
                                onLongPress = {
                                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                    val message = viewModel.getMessageById(item.messageId)
                                    message?.let { onShowAiMessageOptions(it) }
                                },
                                onTap = {},
                                onImageLoaded = onImageLoaded,
                                isStreaming = isApiCalling,
                                renderer = renderer,
                                scrollStateManager = scrollStateManager,
                                modifier = Modifier.onGloballyPositioned { coordinates ->
                                    val newHeight = coordinates.size.height
                                    if (lastHeight != 0 && lastHeight != newHeight) {
                                        if (scrollStateManager.userInteracted && index < listState.firstVisibleItemIndex) {
                                            val heightDiff = newHeight - lastHeight
                                            if (heightDiff != 0) {
                                                coroutineScope.launch {
                                                    listState.scrollBy(heightDiff.toFloat())
                                                }
                                            }
                                        }
                                    }
                                    lastHeight = newHeight
                                }
                            )
                            
                            if (isCodeBlock && item.block is MarkdownBlock.CodeBlock) {
                                CodeBlockActions(
                                    codeBlock = item.block,
                                    maxWidth = bubbleMaxWidth
                                )
                            }
                        }
                    }

                    is ChatListItem.AiMessageFooter -> {
                        AiMessageFooterItem(
                            message = item.message,
                            viewModel = viewModel,
                        )
                    }

                    is ChatListItem.ErrorMessage -> {
                        val message = viewModel.getMessageById(item.messageId)
                        if (message != null) {
                            UserOrErrorMessageContent(
                                message = message,
                                displayedText = item.text,
                                showLoadingDots = false,
                                bubbleColor = MaterialTheme.chatColors.aiBubble,
                                contentColor = MaterialTheme.chatColors.errorContent,
                                isError = true,
                                maxWidth = bubbleMaxWidth,
                                onEditRequest = { viewModel.requestEditMessage(it) },
                                onRegenerateRequest = {
                                    viewModel.regenerateAiResponse(it)
                                    scrollStateManager.jumpToBottom()
                                },
                                scrollStateManager = scrollStateManager,
                            )
                        }
                    }

                    is ChatListItem.LoadingIndicator -> {
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.Bottom,
                            horizontalArrangement = Arrangement.Start
                        ) {
                            Text(
                                text = stringResource(id = R.string.connecting_to_model),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            Spacer(Modifier.width(8.dp))
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = MaterialTheme.chatColors.loadingIndicator,
                                strokeWidth = 2.dp
                            )
                        }
                    }
                }
            }
        }
        item(key = "chat_screen_footer_spacer_in_list") {
            Spacer(modifier = Modifier.height(1.dp))
        }
    }
}

@Composable
private fun AiMessageBlockItem(
    item: ChatListItem.AiMessageBlock,
    maxWidth: Dp,
    onLongPress: () -> Unit,
    onTap: () -> Unit,
    onImageLoaded: () -> Unit,
    isStreaming: Boolean,
    renderer: SimpleMarkdownRenderer,
    scrollStateManager: ChatScrollStateManager,
    modifier: Modifier = Modifier
) {
    val uriHandler = LocalUriHandler.current
    val context = LocalContext.current
    var textLayoutResult by remember { mutableStateOf<androidx.compose.ui.text.TextLayoutResult?>(null) }
    var annotatedString by remember { mutableStateOf<AnnotatedString?>(null) }

    val shape = getBubbleShape(
        isFirstBlock = item.isFirstBlock,
        hasReasoning = item.hasReasoning,
        isLastBlock = item.isLastBlock,
        isCodeBlock = item.block is MarkdownBlock.CodeBlock
    )
    val aiReplyMessageDescription = stringResource(id = R.string.ai_reply_message)

    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Start
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .let { modifier ->
                    if (item.block is MarkdownBlock.CodeBlock) {
                        modifier.semantics {
                            contentDescription = aiReplyMessageDescription
                        }
                    } else {
                        modifier.pointerInput(item.messageId, annotatedString) {
                            detectTapGestures(
                                onLongPress = { onLongPress() },
                                onTap = { offset ->
                                    val currentAnnotatedString = annotatedString
                                    val currentLayoutResult = textLayoutResult
                                    if (currentAnnotatedString != null && currentLayoutResult != null) {
                                        val characterIndex = currentLayoutResult.getOffsetForPosition(offset)
                                        currentAnnotatedString.getStringAnnotations(
                                            tag = "URL",
                                            start = characterIndex,
                                            end = characterIndex
                                        ).firstOrNull()?.let { annotation ->
                                            try {
                                                uriHandler.openUri(annotation.item)
                                            } catch (e: Exception) {
                                                Toast.makeText(context, R.string.cannot_open_link, Toast.LENGTH_SHORT).show()
                                            }
                                        } ?: onTap()
                                    } else {
                                        onTap()
                                    }
                                }
                            )
                        }.semantics {
                            contentDescription = aiReplyMessageDescription
                        }
                    }
                },
            shape = shape,
            color = MaterialTheme.chatColors.aiBubble,
            contentColor = MaterialTheme.colorScheme.onSurface,
            shadowElevation = 0.dp
        ) {
            CompositionLocalProvider(
                LocalOnTextLayout provides { result, string ->
                    textLayoutResult = result
                    annotatedString = string
                }
            ) {
                RenderMarkdownBlock(
                    block = item.block,
                    contentColor = MaterialTheme.colorScheme.onSurface,
                    onImageLoaded = onImageLoaded,
                    isStreaming = isStreaming,
                    renderer = renderer
                )
            }
        }
    }
}

private fun getBubbleShape(isFirstBlock: Boolean, hasReasoning: Boolean, isLastBlock: Boolean, isCodeBlock: Boolean = false): RoundedCornerShape {
    val largeRadius = 16.dp
    val smallRadius = 4.dp
    
    return RoundedCornerShape(
        topStart = if (isFirstBlock && !hasReasoning) largeRadius else smallRadius,
        topEnd = largeRadius,
        bottomStart = if (isCodeBlock) 0.dp else if (isLastBlock) largeRadius else smallRadius,
        bottomEnd = if (isCodeBlock) 0.dp else if (isLastBlock) largeRadius else smallRadius
    )
}

@Composable
private fun AiMessageFooterItem(
    message: Message,
    viewModel: AppViewModel,
) {
    if (!message.webSearchResults.isNullOrEmpty()) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp),
            horizontalArrangement = Arrangement.Start
        ) {
            TextButton(
                onClick = {
                    viewModel.showSourcesDialog(message.webSearchResults)
                },
            ) {
                Text(stringResource(id = R.string.view_sources, message.webSearchResults.size))
            }
        }
    }
}

@Composable
private fun RenderMarkdownBlock(
    block: MarkdownBlock,
    contentColor: Color,
    onImageLoaded: () -> Unit,
    isStreaming: Boolean,
    renderer: SimpleMarkdownRenderer
) {
    when (block) {
        is MarkdownBlock.CodeBlock -> {
            CodeBlock(
                rawText = block.rawText,
                language = block.language,
                contentColor = contentColor
            )
        }
        else -> {
            Box(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                when (block) {
                    is MarkdownBlock.Header -> MarkdownHeader(
                        block = block, 
                        contentColor = contentColor, 
                        isStreaming = isStreaming, 
                        renderer = renderer
                    )

                    is MarkdownBlock.Paragraph -> {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            MarkdownText(
                                text = block.text,
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    color = contentColor,
                                    lineHeight = 26.sp
                                ),
                                isStreaming = isStreaming,
                                renderer = renderer,
                                messageId = block.hashCode().toString()
                            )
                        }
                    }

                    is MarkdownBlock.UnorderedList -> {
                        Column(
                            modifier = Modifier.padding(start = 4.dp),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            block.items.forEach { itemText ->
                                Row(
                                    verticalAlignment = Alignment.Top,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text(
                                        text = "• ",
                                        style = MaterialTheme.typography.bodyLarge.copy(
                                            color = contentColor,
                                            fontWeight = FontWeight.Bold
                                        ),
                                        modifier = Modifier.padding(top = 2.dp)
                                    )
                                    MarkdownText(
                                        text = itemText.trim(),
                                        style = MaterialTheme.typography.bodyLarge.copy(color = contentColor),
                                        modifier = Modifier.weight(1f),
                                        isStreaming = isStreaming,
                                        renderer = renderer,
                                        messageId = itemText.hashCode().toString()
                                    )
                                }
                            }
                        }
                    }

                    is MarkdownBlock.OrderedList -> {
                        Column(
                            modifier = Modifier.padding(start = 4.dp),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            block.items.forEachIndexed { index, itemText ->
                                Row(
                                    verticalAlignment = Alignment.Top,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text(
                                        text = "${index + 1}. ",
                                        style = MaterialTheme.typography.bodyLarge.copy(
                                            color = contentColor,
                                            fontWeight = FontWeight.Bold
                                        ),
                                        modifier = Modifier.padding(top = 2.dp)
                                    )
                                    MarkdownText(
                                        text = itemText.trim(),
                                        style = MaterialTheme.typography.bodyLarge.copy(color = contentColor),
                                        modifier = Modifier.weight(1f),
                                        isStreaming = isStreaming,
                                        renderer = renderer,
                                        messageId = itemText.hashCode().toString()
                                    )
                                }
                            }
                        }
                    }

                    is MarkdownBlock.Blockquote -> {
                        Row {
                            Box(
                                modifier = Modifier
                                    .padding(end = 8.dp)
                                    .width(4.dp)
                                    .fillMaxHeight()
                                    .clip(RoundedCornerShape(2.dp))
                                    .background(contentColor.copy(alpha = 0.3f))
                            )
                            Column {
                                block.blocks.forEach { nestedBlock ->
                                    RenderMarkdownBlock(
                                        block = nestedBlock, 
                                        contentColor = contentColor, 
                                        onImageLoaded = onImageLoaded, 
                                        isStreaming = isStreaming, 
                                        renderer = renderer
                                    )
                                }
                            }
                        }
                    }

                    is MarkdownBlock.HorizontalRule -> {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 16.dp)
                        ) {
                            HorizontalDivider(
                                modifier = Modifier.fillMaxWidth(),
                                thickness = 1.dp,
                                color = contentColor.copy(alpha = 0.3f)
                            )
                        }
                    }

                    is MarkdownBlock.Image -> {
                        val context = LocalContext.current
                        AsyncImage(
                            model = ImageRequest.Builder(context)
                                .data(block.url)
                                .crossfade(true)
                                .build(),
                            contentDescription = block.altText,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp)),
                            onSuccess = { onImageLoaded() }
                        )
                    }

                    is MarkdownBlock.Table -> {
                        // 将表格转换为简单的文本显示
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            // 表格标题
                            if (block.header.isNotEmpty()) {
                                Text(
                                    text = block.header.joinToString(" | "),
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        fontWeight = FontWeight.Bold,
                                        color = contentColor
                                    )
                                )
                                HorizontalDivider(
                                    modifier = Modifier.fillMaxWidth(),
                                    color = contentColor.copy(alpha = 0.3f)
                                )
                            }
                            // 表格行
                            block.rows.forEach { row ->
                                Text(
                                    text = row.joinToString(" | "),
                                    style = MaterialTheme.typography.bodyLarge.copy(color = contentColor)
                                )
                            }
                        }
                    }
                    
                    is MarkdownBlock.MathBlock -> {
                        MarkdownText(
                            text = "$$${block.formula}$$",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                color = Color(0xFF2E7D32),
                                fontFamily = FontFamily.Default
                            ),
                            isStreaming = isStreaming,
                            renderer = renderer,
                            messageId = block.formula.hashCode().toString()
                        )
                    }
                    
                    else -> {
                        // 对于未知类型，尝试提取文本内容
                        val blockText = when (block) {
                            is MarkdownBlock.Header -> block.text
                            is MarkdownBlock.Paragraph -> block.text
                            is MarkdownBlock.CodeBlock -> block.rawText
                            else -> block.toString()
                        }
                        
                        if (blockText.isNotBlank()) {
                            MarkdownText(
                                text = blockText,
                                style = MaterialTheme.typography.bodyLarge.copy(color = contentColor),
                                isStreaming = isStreaming,
                                renderer = renderer,
                                messageId = blockText.hashCode().toString()
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun CodeBlock(
    rawText: String,
    language: String?,
    contentColor: Color
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                Color(0xFFF8F8F8),
                RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
            )
            .border(
                1.dp,
                Color(0xFFE0E0E0),
                RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
            )
    ) {
        // 语言标签
        if (!language.isNullOrBlank()) {
            Surface(
                modifier = Modifier.fillMaxWidth(),
                color = Color(0xFFEEEEEE),
                shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
            ) {
                Text(
                    text = language.uppercase(),
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontWeight = FontWeight.Bold,
                        fontFamily = FontFamily.Monospace
                    ),
                    color = Color(0xFF666666)
                )
            }
        }
        
        // 代码内容
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            Text(
                text = rawText.trim(),
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontFamily = FontFamily.Monospace,
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                ),
                color = Color(0xFF333333)
            )
        }
    }
}

@Composable
private fun CodeBlockActions(
    codeBlock: MarkdownBlock.CodeBlock,
    maxWidth: Dp
) {
    val clipboardManager = LocalClipboardManager.current
    val context = LocalContext.current
    
    Surface(
        onClick = {
            clipboardManager.setText(AnnotatedString(codeBlock.rawText))
            Toast.makeText(context, "代码已复制", Toast.LENGTH_SHORT).show()
        },
        modifier = Modifier
            .widthIn(max = maxWidth)
            .offset(y = (-8).dp),
        shape = RoundedCornerShape(
            topStart = 0.dp,
            topEnd = 0.dp,
            bottomStart = 8.dp,
            bottomEnd = 8.dp
        ),
        color = MaterialTheme.chatColors.codeBlockBackground,
        border = BorderStroke(
            width = 1.dp,
            color = MaterialTheme.chatColors.codeBlockBackground.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Filled.ContentCopy,
                contentDescription = "复制",
                modifier = Modifier.size(16.dp),
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "复制代码",
                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Bold),
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
    
    CodePreviewButton(
        code = codeBlock.rawText,
        language = codeBlock.language,
        modifier = Modifier
            .widthIn(max = maxWidth)
            .offset(y = (-8).dp)
    )
}

@Composable
fun MarkdownHeader(
    block: MarkdownBlock.Header,
    contentColor: Color,
    isStreaming: Boolean = false,
    renderer: SimpleMarkdownRenderer
) {
    val (fontSize, topPadding, bottomPadding) = when (block.level) {
        1 -> Triple(24.sp, 16.dp, 12.dp)
        2 -> Triple(20.sp, 14.dp, 10.dp)
        3 -> Triple(18.sp, 12.dp, 8.dp)
        4 -> Triple(16.sp, 10.dp, 6.dp)
        5 -> Triple(14.sp, 8.dp, 4.dp)
        else -> Triple(12.sp, 6.dp, 2.dp)
    }
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = topPadding, bottom = bottomPadding)
    ) {
        MarkdownText(
            text = block.text,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontSize = fontSize,
                fontWeight = FontWeight.Bold,
                color = contentColor,
                lineHeight = fontSize * 1.2f
            ),
            isStreaming = isStreaming,
            renderer = renderer,
            messageId = block.text.hashCode().toString()
        )
        
        // 为H1和H2添加下划线
        if (block.level <= 2) {
            Spacer(modifier = Modifier.height(4.dp))
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth(if (block.level == 1) 1f else 0.7f),
                thickness = if (block.level == 1) 2.dp else 1.dp,
                color = contentColor.copy(alpha = 0.3f)
            )
        }
    }
}

@Composable
fun MarkdownText(
    text: String,
    modifier: Modifier = Modifier,
    style: TextStyle = LocalTextStyle.current,
    isStreaming: Boolean,
    renderer: SimpleMarkdownRenderer,
    messageId: String
) {
    val onTextLayout = LocalOnTextLayout.current
    
    val annotatedString = remember(text, isStreaming) {
        renderer.renderText(text, isStreaming)
    }

    Text(
        text = annotatedString,
        modifier = modifier.fillMaxWidth(),
        style = style.copy(
            lineHeight = style.lineHeight.takeIf { it.value > 0 } ?: (style.fontSize * 1.5f)
        ),
        softWrap = true,
        overflow = TextOverflow.Visible,
        maxLines = Int.MAX_VALUE,
        onTextLayout = { layoutResult: TextLayoutResult ->
            onTextLayout?.invoke(layoutResult, annotatedString)
        }
    )
}