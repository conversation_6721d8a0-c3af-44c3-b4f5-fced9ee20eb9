"""
Enhanced Markdown Processor
基于 Cherry Studio 的优化实现，提供更好的 Markdown 和数学公式处理
"""

import logging
import re
from typing import Dict, Any, Optional, List, Tuple, Union
import unicodedata

logger = logging.getLogger("EzTalkProxy.Services.EnhancedMarkdownProcessor")


class EnhancedMarkdownProcessor:
    """
    增强的 Markdown 处理器
    基于 Cherry Studio 的实现优化，提供更好的数学公式和格式处理
    """
    
    def __init__(self):
        self.stats = {
            "latex_conversions": 0,
            "math_fixes": 0,
            "code_protections": 0,
            "link_protections": 0,
            "whitespace_fixes": 0,
            "typography_fixes": 0,
            "total_fixes": 0
        }
        
        # LaTeX 检测正则表达式
        self.latex_regex = re.compile(r'\\\(.*?\\\)|\\\[.*?\\\]', re.DOTALL)
        
        # 保护项存储
        self.protected_items = []
    
    def process_content(self, text: str, request_id: str = "", model_type: str = "general") -> str:
        """
        处理 AI 输出的 Markdown 内容
        
        Args:
            text: 原始文本
            request_id: 请求ID
            model_type: 模型类型
            
        Returns:
            处理后的文本
        """
        if not isinstance(text, str) or not text.strip():
            return text
        
        log_prefix = f"RID-{request_id}" if request_id else "EnhancedMarkdownProcessor"
        original_length = len(text)
        
        logger.info(f"{log_prefix}: Processing {original_length} chars for {model_type}")
        
        # 重置统计和保护项
        self.stats = {k: 0 for k in self.stats}
        self.protected_items = []
        
        try:
            # 1. LaTeX 括号转换（核心功能）
            text = self.process_latex_brackets(text)
            
            # 2. 数学公式优化
            text = self._enhance_math_formulas(text)
            
            # 3. 代码块优化
            text = self._enhance_code_blocks(text)
            
            # 4. 表格格式优化
            text = self._enhance_table_formatting(text)
            
            # 5. 列表格式优化
            text = self._enhance_list_formatting(text)
            
            # 6. 空白字符优化
            text = self._optimize_whitespace(text)
            
            # 7. 中英文排版优化
            text = self._optimize_typography(text)
            
            # 8. 最终清理
            text = self._final_cleanup(text)
            
            final_length = len(text)
            self.stats["total_fixes"] = sum(v for k, v in self.stats.items() if k != "total_fixes")
            
            logger.info(f"{log_prefix}: Processing completed. {original_length} -> {final_length} chars. "
                       f"Fixes: {self.stats['total_fixes']}")
            
            return text
            
        except Exception as e:
            logger.error(f"{log_prefix}: Error processing markdown: {e}", exc_info=True)
            return text
    
    def process_latex_brackets(self, text: str) -> str:
        """
        转换 LaTeX 公式括号为 Markdown 格式
        基于 Cherry Studio 的实现
        
        \[...\] -> $$...$$
        \(...\) -> $...$
        """
        # 如果没有 LaTeX 模式，直接返回
        if not self.latex_regex.search(text):
            return text
        
        # 保护代码块和链接
        protected_content = self._protect_content(text)
        
        # 处理 LaTeX 括号转换
        result = self._process_math_delimiters(protected_content, '\\[', '\\]', '$$')
        result = self._process_math_delimiters(result, '\\(', '\\)', '$')
        
        # 还原保护的内容
        result = self._restore_protected_content(result)
        
        return result
    
    def _protect_content(self, text: str) -> str:
        """保护代码块和链接不被处理"""
        self.protected_items = []
        processed_text = text
        
        # 保护代码块（多行和行内）
        processed_text = re.sub(
            r'(```[\s\S]*?```|`[^`]*`)',
            self._create_protection_placeholder,
            processed_text
        )
        
        # 保护链接 [text](url)
        processed_text = re.sub(
            r'\[([^[\]]*(?:\[[^\]]*\][^[\]]*)*)\]\([^)]*?\)',
            self._create_protection_placeholder,
            processed_text
        )
        
        self.stats["code_protections"] = len([item for item in self.protected_items if '```' in item or '`' in item])
        self.stats["link_protections"] = len([item for item in self.protected_items if '[' in item and '](' in item])
        
        return processed_text
    
    def _create_protection_placeholder(self, match) -> str:
        """创建保护占位符"""
        index = len(self.protected_items)
        self.protected_items.append(match.group(0))
        return f"__ENHANCED_MD_PROTECTED_{index}__"
    
    def _restore_protected_content(self, text: str) -> str:
        """还原被保护的内容"""
        def replace_placeholder(match):
            index_str = match.group(1)
            try:
                index = int(index_str)
                if 0 <= index < len(self.protected_items):
                    return self.protected_items[index]
            except (ValueError, IndexError):
                pass
            return match.group(0)
        
        return re.sub(r'__ENHANCED_MD_PROTECTED_(\d+)__', replace_placeholder, text)
    
    def _process_math_delimiters(self, content: str, open_delim: str, close_delim: str, wrapper: str) -> str:
        """处理数学分隔符转换"""
        result = ''
        remaining = content
        
        while remaining:
            match = self._find_latex_match(remaining, open_delim, close_delim)
            if not match:
                result += remaining
                break
            
            result += match['pre']
            result += f"{wrapper}{match['body']}{wrapper}"
            remaining = match['post']
            self.stats["latex_conversions"] += 1
        
        return result
    
    def _find_latex_match(self, text: str, open_delim: str, close_delim: str) -> Optional[Dict[str, str]]:
        """
        查找 LaTeX 数学公式的匹配括号对
        使用平衡括号算法处理嵌套结构
        """
        def is_escaped(pos: int) -> bool:
            """检查位置是否被转义"""
            count = 0
            i = pos - 1
            while i >= 0 and text[i] == '\\':
                count += 1
                i -= 1
            return count % 2 == 1
        
        # 查找第一个有效的开始标记
        text_len = len(text)
        open_len = len(open_delim)
        close_len = len(close_delim)
        
        for i in range(text_len - open_len + 1):
            if not text.startswith(open_delim, i) or is_escaped(i):
                continue
            
            # 处理嵌套结构
            depth = 1
            j = i + open_len
            
            while j <= text_len - close_len and depth > 0:
                if text.startswith(open_delim, j) and not is_escaped(j):
                    depth += 1
                    j += open_len
                elif text.startswith(close_delim, j) and not is_escaped(j):
                    depth -= 1
                    if depth == 0:
                        return {
                            'start': i,
                            'end': j + close_len,
                            'pre': text[:i],
                            'body': text[i + open_len:j],
                            'post': text[j + close_len:]
                        }
                    j += close_len
                else:
                    j += 1
        
        return None
    
    def _enhance_math_formulas(self, text: str) -> str:
        """增强数学公式处理"""
        if '$' not in text:
            return text
        
        original_text = text
        
        # 修复常见的 LaTeX 语法问题
        math_fixes = {
            # 分数格式
            r'\\frac\s+(\w+)\s+(\w+)': r'\\frac{\1}{\2}',
            r'\\frac\s*\{\s*([^}]+)\s*\}\s*\{\s*([^}]+)\s*\}': r'\\frac{\1}{\2}',
            
            # 根号格式
            r'\\sqrt\s+(\w+)': r'\\sqrt{\1}',
            r'\\sqrt\s*\{\s*([^}]+)\s*\}': r'\\sqrt{\1}',
            
            # 上下标格式
            r'([a-zA-Z0-9])\s*\^\s*([^{\s]+)': r'\1^{\2}',
            r'([a-zA-Z0-9])\s*_\s*([^{\s]+)': r'\1_{\2}',
            
            # 求和和积分
            r'\\sum\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)': r'\\sum_{\1}^{\2}',
            r'\\int\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)': r'\\int_{\1}^{\2}',
            
            # 清理多余空格
            r'\$\s+': r'$',
            r'\s+\$': r'$',
            r'\$\$\s+': r'$$',
            r'\s+\$\$': r'$$',
            
            # 大括号空格
            r'\{\s+': r'{',
            r'\s+\}': r'}',
        }
        
        for pattern, replacement in math_fixes.items():
            new_text = re.sub(pattern, replacement, text)
            if new_text != text:
                self.stats["math_fixes"] += 1
                text = new_text
        
        # 确保数学分隔符平衡
        text = self._balance_math_delimiters(text)
        
        return text
    
    def _balance_math_delimiters(self, text: str) -> str:
        """确保数学分隔符平衡"""
        # 统计单个 $ 符号
        single_dollar_count = 0
        result = []
        i = 0
        
        while i < len(text):
            if i < len(text) - 1 and text[i:i+2] == '$$':
                result.append('$$')
                i += 2
            elif text[i] == '$':
                single_dollar_count += 1
                result.append('$')
                i += 1
            else:
                result.append(text[i])
                i += 1
        
        # 如果单个 $ 符号数量是奇数，添加一个 $
        if single_dollar_count % 2 == 1:
            result.append('$')
            self.stats["math_fixes"] += 1
        
        return ''.join(result)
    
    def _enhance_code_blocks(self, text: str) -> str:
        """增强代码块处理"""
        if '```' not in text:
            return text
        
        # 修复代码块格式
        # 确保代码块前后有换行
        text = re.sub(r'([^\n])```', r'\1\n```', text)
        text = re.sub(r'```([^\n])', r'```\n\1', text)
        
        # 修复语言标识符
        text = re.sub(r'```\s*([a-zA-Z]+)\s*\n', r'```\1\n', text)
        
        return text
    
    def _enhance_table_formatting(self, text: str) -> str:
        """增强表格格式处理"""
        if '|' not in text:
            return text
        
        lines = text.split('\n')
        result = []
        
        for line in lines:
            if '|' in line and line.strip():
                # 清理表格行
                cells = [cell.strip() for cell in line.split('|')]
                # 移除首尾空元素
                if cells and not cells[0]:
                    cells = cells[1:]
                if cells and not cells[-1]:
                    cells = cells[:-1]
                
                if cells:
                    formatted_line = '| ' + ' | '.join(cells) + ' |'
                    result.append(formatted_line)
                else:
                    result.append(line)
            else:
                result.append(line)
        
        return '\n'.join(result)
    
    def _enhance_list_formatting(self, text: str) -> str:
        """增强列表格式处理"""
        lines = text.split('\n')
        result = []
        
        for line in lines:
            stripped = line.strip()
            
            # 处理无序列表
            if re.match(r'^[-*+]\s+', stripped):
                # 确保列表项格式正确
                indent = len(line) - len(line.lstrip())
                marker = stripped[0]
                content = stripped[2:].strip()
                formatted_line = ' ' * indent + f'{marker} {content}'
                result.append(formatted_line)
            
            # 处理有序列表
            elif re.match(r'^\d+\.\s+', stripped):
                indent = len(line) - len(line.lstrip())
                match = re.match(r'^(\d+)\.\s+(.*)$', stripped)
                if match:
                    number, content = match.groups()
                    formatted_line = ' ' * indent + f'{number}. {content.strip()}'
                    result.append(formatted_line)
                else:
                    result.append(line)
            else:
                result.append(line)
        
        return '\n'.join(result)
    
    def _optimize_whitespace(self, text: str) -> str:
        """优化空白字符"""
        # 移除行尾空格
        text = re.sub(r' +$', '', text, flags=re.MULTILINE)
        
        # 限制连续空行
        text = re.sub(r'\n{4,}', '\n\n\n', text)
        
        # 修复段落间距
        text = re.sub(r'\n\n+', '\n\n', text)
        
        self.stats["whitespace_fixes"] += 1
        return text
    
    def _optimize_typography(self, text: str) -> str:
        """优化中英文排版"""
        # 中英文之间添加空格
        text = re.sub(r'([\u4e00-\u9fff])([a-zA-Z0-9])', r'\1 \2', text)
        text = re.sub(r'([a-zA-Z0-9])([\u4e00-\u9fff])', r'\1 \2', text)
        
        # 中文和数字之间添加空格
        text = re.sub(r'([\u4e00-\u9fff])(\d)', r'\1 \2', text)
        text = re.sub(r'(\d)([\u4e00-\u9fff])', r'\1 \2', text)
        
        # 修复标点符号
        text = re.sub(r'([\u4e00-\u9fff])\s*([，。！？；：])', r'\1\2', text)
        
        self.stats["typography_fixes"] += 1
        return text
    
    def _final_cleanup(self, text: str) -> str:
        """最终清理"""
        # 移除文档开头和结尾的多余空行
        text = text.strip()
        
        # 确保文档以换行符结尾
        if text and not text.endswith('\n'):
            text += '\n'
        
        return text
    
    def get_processing_stats(self) -> Dict[str, int]:
        """获取处理统计信息"""
        return self.stats.copy()


# 创建全局实例
enhanced_markdown_processor = EnhancedMarkdownProcessor()