import logging
import re
from typing import Dict, Any, Optional, List, Tuple
import unicodedata
from .advanced_typography import advanced_typography_processor
from .adaptive_formatter import adaptive_formatter
from .quality_assessor import quality_assessor


logger = logging.getLogger("EzTalkProxy.Services.MarkdownProcessor")

class UnifiedMarkdownProcessor:
    """
    统一的Markdown和数学公式处理器
    适用于所有大模型的输出格式化
    """
    
    def __init__(self):
        self.stats = {
            "math_fixes": 0,
            "code_fixes": 0,
            "table_fixes": 0,
            "list_fixes": 0,
            "whitespace_fixes": 0,
            "typography_fixes": 0,
            "paragraph_fixes": 0,
            "punctuation_fixes": 0
        }

        # 智能排版配置
        self.typography_config = {
            "enable_smart_quotes": True,
            "enable_paragraph_optimization": True,
            "enable_punctuation_spacing": True,
            "enable_chinese_english_spacing": True,
            "max_consecutive_newlines": 3,
            "preserve_code_formatting": True
        }
    
    def process_content(self, text: str, request_id: str = "", model_type: str = "general") -> str:
        """
        智能Markdown内容处理 - 根据模型类型应用不同的处理策略
        
        Args:
            text: 原始文本
            request_id: 请求ID用于日志
            model_type: 模型类型 ("gemini", "openai", "claude", "deepseek", "qwen", "general")
        
        Returns:
            处理后的文本
        """
        if not isinstance(text, str) or not text.strip():
            return text
        
        log_prefix = f"RID-{request_id}" if request_id else "MarkdownProcessor"
        original_length = len(text)
        
        logger.info(f"{log_prefix}: Enhanced processing {original_length} chars for {model_type} model")
        
        # 重置统计
        self.stats = {k: 0 for k in self.stats}
        
        try:
            # 智能处理流程 - 根据模型类型调整处理强度
            
            # 1. 数学公式修复（支持KaTeX优化）
            if '$' in text:
                text = self._fix_math_formulas(text, log_prefix)
                text = self._enhance_math_formulas(text)
            
            # 2. 代码块修复（增强语言检测）
            if '```' in text:
                text = self._fix_code_blocks(text, log_prefix)
                text = self._enhance_code_blocks(text, model_type)
            
            # 3. 表格修复（智能对齐优化）
            if '|' in text:
                text = self._fix_table_formatting(text, log_prefix)
            
            # 4. 列表修复（层次结构优化）
            text = self._fix_list_formatting(text, log_prefix)
            text = self._enhance_list_formatting(text)
            
            # 5. 模型特定优化
            text = self._apply_model_specific_fixes(text, model_type, log_prefix)
            
            # 6. 内容质量优化
            text = self._apply_content_quality_optimization(text, model_type, log_prefix)
            
            # 7. 智能段落结构优化
            text = self._optimize_paragraph_structure_enhanced(text, log_prefix)
            
            # 8. 智能空白处理
            text = self._optimize_whitespace_enhanced(text, log_prefix)

            # 9. 智能排版处理
            text = self._apply_smart_typography_enhanced(text, model_type, log_prefix)
            
            # 10. 最终质量检查和清理
            text = self._final_cleanup_enhanced(text, log_prefix)
            
            final_length = len(text)
            logger.info(f"{log_prefix}: Enhanced processing completed. {original_length} -> {final_length} chars. Fixes: {self.stats}")

            return text
            
        except Exception as e:
            logger.error(f"{log_prefix}: Error processing markdown: {e}", exc_info=True)
            return text
    
    def _fix_math_formulas(self, text: str, log_prefix: str) -> str:
        """修复数学公式格式"""
        if '$' not in text:
            return text
        
        original_text = text
        
        # 1. 修复LaTeX语法错误
        fixes = {
            # 分数格式化
            r'\\frac\s+(\w+)\s+(\w+)': r'\\frac{\1}{\2}',
            r'\\frac\s*\{\s*([^}]+)\s*\}\s*\{\s*([^}]+)\s*\}': r'\\frac{\1}{\2}',
            
            # 根号格式化
            r'\\sqrt\s+(\w+)': r'\\sqrt{\1}',
            r'\\sqrt\s*\{\s*([^}]+)\s*\}': r'\\sqrt{\1}',
            
            # 求和和积分
            r'\\sum\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)': r'\\sum_{\1}^{\2}',
            r'\\int\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)': r'\\int_{\1}^{\2}',
            
            # 上下标
            r'([a-zA-Z])\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)': r'\1_{\2}^{\3}',
            r'([a-zA-Z])\s*\^\s*([^{\s]+)\s*_\s*([^{\s]+)': r'\1^{\2}_{\3}',
            
            # 数学函数
            r'\\log\s+([^{\s]+)': r'\\log{\1}',
            r'\\ln\s+([^{\s]+)': r'\\ln{\1}',
            
            # 修复空格问题
            r'\$\s+': r'$',
            r'\s+\$': r'$',
            r'\$\$\s+': r'$$',
            r'\s+\$\$': r'$$',
            
            # 大括号空格
            r'\{\s+': r'{',
            r'\s+\}': r'}',
            
            # 矩阵和向量
            r'\\begin\s*\{\s*([a-zA-Z]+)\s*\}': r'\\begin{\1}',
            r'\\end\s*\{\s*([a-zA-Z]+)\s*\}': r'\\end{\1}',
        }
        
        for pattern, replacement in fixes.items():
            new_text = re.sub(pattern, replacement, text)
            if new_text != text:
                self.stats["math_fixes"] += 1
                text = new_text
        
        # 2. 确保数学分隔符平衡
        text = self._balance_math_delimiters(text)
        
        # 3. 修复常见的KaTeX兼容性问题
        text = self._fix_katex_compatibility(text)
        
        if text != original_text:
            logger.info(f"{log_prefix}: Applied {self.stats['math_fixes']} math formula fixes")
        
        return text
    
    def _balance_math_delimiters(self, text: str) -> str:
        """确保数学分隔符平衡"""
        # 处理单个$符号
        single_dollar_count = 0
        result = []
        i = 0
        
        while i < len(text):
            if i < len(text) - 1 and text[i:i+2] == '$$':
                result.append('$$')
                i += 2
            elif text[i] == '$':
                single_dollar_count += 1
                result.append('$')
                i += 1
            else:
                result.append(text[i])
                i += 1
        
        # 如果单个$符号数量是奇数，添加一个$
        if single_dollar_count % 2 == 1:
            result.append('$')
            self.stats["math_fixes"] += 1
        
        return ''.join(result)
    
    def _fix_katex_compatibility(self, text: str) -> str:
        """修复KaTeX兼容性问题"""
        katex_fixes = {
            # 常见的LaTeX命令转KaTeX
            r'\\displaystyle': '',  # KaTeX自动处理显示样式
            r'\\text\s*\{\s*([^}]+)\s*\}': r'\\text{\1}',  # 清理文本命令
            r'\\mathrm\s*\{\s*([^}]+)\s*\}': r'\\mathrm{\1}',  # 罗马字体
            r'\\mathbf\s*\{\s*([^}]+)\s*\}': r'\\mathbf{\1}',  # 粗体
            r'\\mathit\s*\{\s*([^}]+)\s*\}': r'\\mathit{\1}',  # 斜体

            # 修复align环境
            r'\\begin\s*\{\s*align\s*\}': r'\\begin{aligned}',
            r'\\end\s*\{\s*align\s*\}': r'\\end{aligned}',
            r'\\begin\s*\{\s*align\*\s*\}': r'\\begin{aligned}',
            r'\\end\s*\{\s*align\*\s*\}': r'\\end{aligned}',

            # 增强的数学函数处理
            r'\\arcsin': r'\\arcsin',
            r'\\arccos': r'\\arccos',
            r'\\arctan': r'\\arctan',
            r'\\sinh': r'\\sinh',
            r'\\cosh': r'\\cosh',
            r'\\tanh': r'\\tanh',

            # 修复常见的希腊字母
            r'\\alpha': r'\\alpha',
            r'\\beta': r'\\beta',
            r'\\gamma': r'\\gamma',
            r'\\delta': r'\\delta',
            r'\\epsilon': r'\\epsilon',
            r'\\theta': r'\\theta',
            r'\\lambda': r'\\lambda',
            r'\\mu': r'\\mu',
            r'\\pi': r'\\pi',
            r'\\sigma': r'\\sigma',
            r'\\phi': r'\\phi',
            r'\\omega': r'\\omega',

            # 修复数学符号
            r'\\infty': r'\\infty',
            r'\\partial': r'\\partial',
            r'\\nabla': r'\\nabla',
            r'\\pm': r'\\pm',
            r'\\mp': r'\\mp',
            r'\\times': r'\\times',
            r'\\div': r'\\div',
            r'\\cdot': r'\\cdot',
            r'\\leq': r'\\leq',
            r'\\geq': r'\\geq',
            r'\\neq': r'\\neq',
            r'\\approx': r'\\approx',
            r'\\equiv': r'\\equiv',

            # 修复集合符号
            r'\\in': r'\\in',
            r'\\notin': r'\\notin',
            r'\\subset': r'\\subset',
            r'\\supset': r'\\supset',
            r'\\subseteq': r'\\subseteq',
            r'\\supseteq': r'\\supseteq',
            r'\\cup': r'\\cup',
            r'\\cap': r'\\cap',
            r'\\emptyset': r'\\emptyset',

            # 修复箭头符号
            r'\\rightarrow': r'\\rightarrow',
            r'\\leftarrow': r'\\leftarrow',
            r'\\leftrightarrow': r'\\leftrightarrow',
            r'\\Rightarrow': r'\\Rightarrow',
            r'\\Leftarrow': r'\\Leftarrow',
            r'\\Leftrightarrow': r'\\Leftrightarrow',
        }

        for pattern, replacement in katex_fixes.items():
            new_text = re.sub(pattern, replacement, text)
            if new_text != text:
                self.stats["math_fixes"] += 1
                text = new_text

        # 增强的数学公式验证和修复
        text = self._enhance_math_formulas(text)

        return text

    def _enhance_math_formulas(self, text: str) -> str:
        """增强数学公式处理"""
        # 修复常见的数学表达式格式问题

        # 1. 修复分数表达式
        text = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', text)
        text = re.sub(r'([a-zA-Z]+)/([a-zA-Z]+)', r'\\frac{\1}{\2}', text)

        # 2. 修复指数表达式
        text = re.sub(r'([a-zA-Z0-9])\^([a-zA-Z0-9])', r'\1^{\2}', text)
        text = re.sub(r'([a-zA-Z0-9])_([a-zA-Z0-9])', r'\1_{\2}', text)

        # 3. 修复根号表达式
        text = re.sub(r'sqrt\(([^)]+)\)', r'\\sqrt{\1}', text)

        # 4. 修复三角函数
        trig_functions = ['sin', 'cos', 'tan', 'sec', 'csc', 'cot']
        for func in trig_functions:
            text = re.sub(f'{func}\\(([^)]+)\\)', f'\\\\{func}(\\1)', text)

        # 5. 修复对数函数
        text = re.sub(r'log\(([^)]+)\)', r'\\log(\1)', text)
        text = re.sub(r'ln\(([^)]+)\)', r'\\ln(\1)', text)

        # 6. 修复求和和积分
        text = re.sub(r'sum\(([^)]+)\)', r'\\sum(\1)', text)
        text = re.sub(r'int\(([^)]+)\)', r'\\int(\1)', text)

        return text
    
    def _fix_code_blocks(self, text: str, log_prefix: str) -> str:
        """修复代码块格式"""
        if '```' not in text:
            return text
        
        original_text = text
        
        # 1. 统计代码块标记
        code_block_count = text.count('```')
        if code_block_count % 2 != 0:
            # 奇数个标记，添加闭合标记
            text += '\n```'
            self.stats["code_fixes"] += 1
            logger.info(f"{log_prefix}: Added missing code block closing marker")
        
        # 2. 修复内联代码块标记
        lines = text.split('\n')
        fixed_lines = []
        in_code_block = False
        
        for line in lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                # 清理语言标识符
                if in_code_block and len(line.strip()) > 3:
                    lang = line.strip()[3:].strip()
                    fixed_lines.append(f"```{lang}")
                else:
                    fixed_lines.append("```")
            elif '```' in line and not line.strip().startswith('```'):
                # 处理行中间的代码块标记
                parts = line.split('```')
                if len(parts) >= 2:
                    new_line = parts[0].rstrip()
                    if new_line:
                        fixed_lines.append(new_line)
                    
                    for j in range(1, len(parts)):
                        if j % 2 == 1:  # 开始标记
                            lang = parts[j].split()[0] if parts[j].strip() else ""
                            fixed_lines.append(f"```{lang}")
                            if len(parts[j].split()) > 1:
                                fixed_lines.append(' '.join(parts[j].split()[1:]))
                        else:  # 结束标记
                            if parts[j].strip():
                                fixed_lines.append(parts[j].lstrip())
                            fixed_lines.append("```")
                    self.stats["code_fixes"] += 1
                else:
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        
        text = '\n'.join(fixed_lines)
        
        if text != original_text:
            logger.info(f"{log_prefix}: Applied {self.stats['code_fixes']} code block fixes")
        
        return text
    
    def _fix_table_formatting(self, text: str, log_prefix: str) -> str:
        """修复表格格式"""
        if '|' not in text:
            return text
        
        original_text = text
        lines = text.split('\n')
        fixed_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            if '|' in line and line.strip() and not self._is_code_line(line, lines, i):
                # 检测表格开始
                table_lines = []
                j = i
                
                # 收集所有表格行
                while j < len(lines) and '|' in lines[j] and lines[j].strip():
                    table_lines.append(lines[j])
                    j += 1
                
                if len(table_lines) >= 1:
                    # 修复表格格式
                    fixed_table = self._fix_table_lines(table_lines)
                    fixed_lines.extend(fixed_table)
                    self.stats["table_fixes"] += 1
                
                i = j
            else:
                fixed_lines.append(line)
                i += 1
        
        text = '\n'.join(fixed_lines)
        
        if text != original_text:
            logger.info(f"{log_prefix}: Applied {self.stats['table_fixes']} table fixes")
        
        return text
    
    def _is_code_line(self, line: str, all_lines: list, line_index: int) -> bool:
        """检查是否是代码块内的行"""
        code_block_count = 0
        for i in range(line_index):
            if '```' in all_lines[i]:
                code_block_count += all_lines[i].count('```')
        
        return code_block_count % 2 == 1
    
    def _fix_table_lines(self, table_lines: list) -> list:
        """修复表格行格式"""
        if not table_lines:
            return []

        fixed = []
        max_columns = 0

        # 第一遍：确定最大列数
        for line in table_lines:
            if not self._is_table_separator(line):
                cells = [cell.strip() for cell in line.split('|')]
                # 移除开头和结尾的空单元格
                if cells and cells[0] == '':
                    cells = cells[1:]
                if cells and cells[-1] == '':
                    cells = cells[:-1]
                max_columns = max(max_columns, len(cells))

        # 第二遍：格式化所有行
        separator_added = False
        for idx, line in enumerate(table_lines):
            if self._is_table_separator(line):
                # 重新生成分隔符以确保列数一致
                separator = '| ' + ' | '.join(['---'] * max_columns) + ' |'
                fixed.append(separator)
                separator_added = True
            else:
                # 处理数据行
                cells = [cell.strip() for cell in line.split('|')]

                # 移除开头和结尾的空单元格
                if cells and cells[0] == '':
                    cells = cells[1:]
                if cells and cells[-1] == '':
                    cells = cells[:-1]

                # 补齐列数
                while len(cells) < max_columns:
                    cells.append('')

                # 智能对齐处理
                aligned_cells = self._align_table_cells(cells, idx == 0)

                if aligned_cells:
                    formatted_line = '| ' + ' | '.join(aligned_cells) + ' |'
                    fixed.append(formatted_line)

                    # 在第一行后添加分隔符（如果缺失）
                    if idx == 0 and not separator_added and len(table_lines) > 1:
                        next_line = table_lines[1] if idx + 1 < len(table_lines) else ""
                        if not self._is_table_separator(next_line):
                            separator = '| ' + ' | '.join(['---'] * len(aligned_cells)) + ' |'
                            fixed.append(separator)
                            separator_added = True

        return fixed

    def _align_table_cells(self, cells: list, is_header: bool = False) -> list:
        """智能对齐表格单元格"""
        aligned = []

        for cell in cells:
            # 清理单元格内容
            cleaned_cell = cell.strip()

            # 如果是数字，右对齐；如果是文本，左对齐
            if cleaned_cell.replace('.', '').replace('-', '').replace('+', '').isdigit():
                # 数字内容，保持原样（Markdown表格对齐由CSS处理）
                aligned.append(cleaned_cell)
            else:
                # 文本内容，确保适当的间距
                aligned.append(cleaned_cell)

        return aligned
    
    def _is_table_separator(self, line: str) -> bool:
        """检查是否是表格分隔符行"""
        return bool(re.match(r'^\s*\|[\s\-:]+\|\s*$', line))
    
    def _fix_list_formatting(self, text: str, log_prefix: str) -> str:
        """修复列表格式"""
        original_text = text
        
        # 1. 修复重复的列表标记
        text = self._fix_duplicate_list_markers(text)
        
        # 2. 重新编号有序列表
        text = self._renumber_ordered_lists(text)
        
        if text != original_text:
            self.stats["list_fixes"] += 1
            logger.info(f"{log_prefix}: Applied list formatting fixes")
        
        return text
    
    def _fix_duplicate_list_markers(self, text: str) -> str:
        """修复重复的列表标记"""
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            if '|' in line:  # 跳过表格行
                cleaned_lines.append(line)
                continue
            
            # 修复重复的数字列表标记
            duplicate_number_pattern = r'^(\s*)(\d+\.\s+)+(\d+\.\s+)(.*)$'
            match = re.match(duplicate_number_pattern, line)
            if match:
                indent = match.group(1)
                last_number = match.group(3)
                content = match.group(4)
                cleaned_lines.append(f"{indent}{last_number}{content}")
                continue
            
            # 修复重复的项目符号
            duplicate_bullet_pattern = r'^(\s*)([*+-]\s+)+([*+-]\s+)(.*)$'
            match = re.match(duplicate_bullet_pattern, line)
            if match:
                indent = match.group(1)
                bullet = match.group(3)
                content = match.group(4)
                cleaned_lines.append(f"{indent}{bullet}{content}")
                continue
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _renumber_ordered_lists(self, lines_text: str) -> str:
        """重新编号有序列表"""
        lines = lines_text.split('\n')
        result = []
        indent_counters = {}
        
        # 检查是否有重复编号问题
        has_duplicate_numbering = False
        first_items = [line for line in lines if re.match(r'^\s*1\.\s+.*', line.strip())]
        if len(first_items) > 1:
            has_duplicate_numbering = True
        
        if not has_duplicate_numbering:
            return lines_text
        
        for line in lines:
            stripped_line = line.strip()
            
            # 检查是否是有序列表项
            ordered_list_match = re.match(r'^(\d+)\.\s+(.*)$', stripped_line)
            
            if ordered_list_match:
                content = ordered_list_match.group(2)
                current_indent = len(line) - len(line.lstrip())
                
                # 初始化或递增计数器
                if current_indent not in indent_counters:
                    indent_counters[current_indent] = 1
                else:
                    indent_counters[current_indent] += 1
                
                # 重置更深层次的计数器
                keys_to_remove = [k for k in indent_counters.keys() if k > current_indent]
                for k in keys_to_remove:
                    del indent_counters[k]
                
                result.append(' ' * current_indent + f"{indent_counters[current_indent]}. {content}")
            else:
                # 非有序列表项
                if stripped_line and not re.match(r'^[*+-]\s+.*', stripped_line):
                    # 遇到非列表内容时，可能需要重置计数器
                    if not any(re.match(r'^\s*\d+\.\s+.*', l.strip()) for l in lines[lines.index(line)+1:lines.index(line)+3] if lines.index(line)+1 < len(lines)):
                        indent_counters.clear()
                result.append(line)
        
        return '\n'.join(result)
    
    def _apply_gemini_specific_fixes(self, text: str, log_prefix: str) -> str:
        """应用Gemini特定的修复"""
        # Gemini经常在联网搜索结果中产生格式问题
        
        # 1. 修复搜索结果中的引用格式
        citation_pattern = r'\[(\d+)\]\s*\(([^)]+)\)'
        text = re.sub(citation_pattern, r'[\1](\2)', text)
        
        # 2. 修复思考标记的格式
        text = re.sub(r'<think>\s*([^<]+)\s*</think>', r'<思考>\n\1\n</思考>', text)
        
        return text
    
    def _apply_openai_specific_fixes(self, text: str, log_prefix: str) -> str:
        """应用OpenAI特定的修复"""
        # OpenAI模型经常在reasoning输出中有格式问题
        
        # 1. 修复reasoning标记
        text = re.sub(r'<reasoning>\s*([^<]+)\s*</reasoning>', r'**推理过程：**\n\1', text)
        
        # 2. 修复工具调用格式
        text = re.sub(r'```json\s*(\{[^}]+\})\s*```', r'```json\n\1\n```', text)
        
        return text
    
    def _optimize_whitespace(self, text: str, log_prefix: str) -> str:
        """极度保守的空白字符优化"""
        original_text = text
        
        # 1. 极度保守的换行清理 - 只清理明显过度的空行（10个或更多）
        text = re.sub(r'\n{11,}', '\n\n\n\n', text)
        
        # 2. 保留更多自然的段落分隔 - 只处理极端情况
        text = re.sub(r'([：:。！？])\n{8,}', r'\1\n\n\n\n', text)
        
        # 3. 分隔符后的换行处理也更保守
        text = re.sub(r'(---)\n{8,}', r'\1\n\n\n\n', text)
        
        # 4. 只移除明显多余的行尾空白（3个或更多空格/制表符）
        text = re.sub(r'[ \t]{3,}$', '', text, flags=re.MULTILINE)
        text = re.sub(r'^[ \t]{3,}$', '', text, flags=re.MULTILINE)
        
        # 5. 不处理空白行，完全保留原始结构
        
        if text != original_text:
            self.stats["whitespace_fixes"] += 1
        
        return text
    
    def _final_cleanup(self, text: str, log_prefix: str) -> str:
        """最终清理"""
        # 1. 修复markdown标记周围的空格
        text = re.sub(r'\*\*\s+(.*?)\s+\*\*', r'**\1**', text)
        text = re.sub(r'\*\s+(.*?)\s+\*', r'*\1*', text)
        
        # 2. 修复链接格式
        text = re.sub(r'\[\s+(.*?)\s+\]\s*\(\s*(.*?)\s*\)', r'[\1](\2)', text)
        
        # 3. 最终空白清理
        text = text.strip()
        
        # 4. 确保文档结构完整
        if text and not text.endswith('\n') and ('\n' in text):
            text += '\n'
        
        return text

    def _apply_smart_typography(self, text: str, log_prefix: str) -> str:
        """应用智能排版优化"""
        if not self.typography_config["enable_smart_quotes"] and not self.typography_config["enable_punctuation_spacing"]:
            return text

        original_text = text

        # 1. 智能引号处理
        if self.typography_config["enable_smart_quotes"]:
            text = self._fix_smart_quotes(text)

        # 2. 标点符号间距优化
        if self.typography_config["enable_punctuation_spacing"]:
            text = self._optimize_punctuation_spacing(text)

        # 3. 中英文混排优化
        if self.typography_config["enable_chinese_english_spacing"]:
            text = self._optimize_chinese_english_spacing(text)

        if text != original_text:
            self.stats["typography_fixes"] += 1
            logger.info(f"{log_prefix}: Applied smart typography fixes")

        return text

    def _fix_smart_quotes(self, text: str) -> str:
        """修复智能引号"""
        # 避免在代码块中处理
        lines = text.split('\n')
        result = []
        in_code_block = False

        for line in lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                result.append(line)
                continue

            if not in_code_block:
                # 修复中文引号
                line = re.sub(r'"([^"]*)"', r'"\1"', line)
                line = re.sub(r"'([^']*)'", r"'\1'", line)

                # 修复英文引号配对
                line = re.sub(r'\s"([^"]+)"\s', r' "\1" ', line)

            result.append(line)

        return '\n'.join(result)

    def _optimize_punctuation_spacing(self, text: str) -> str:
        """优化标点符号间距"""
        # 中文标点后不需要空格，英文标点后需要空格

        # 中文标点符号处理
        chinese_punctuation = '，。！？；：""''（）【】《》'
        for punct in chinese_punctuation:
            # 移除中文标点后的多余空格
            text = re.sub(f'{re.escape(punct)}\\s+', punct, text)

        # 英文标点符号处理
        english_punctuation = ',.!?;:'
        for punct in english_punctuation:
            # 确保英文标点后有适当空格（但不在行尾）
            text = re.sub(f'{re.escape(punct)}(?=[a-zA-Z0-9])', f'{punct} ', text)

        # 修复括号间距
        text = re.sub(r'\(\s+', '(', text)
        text = re.sub(r'\s+\)', ')', text)
        text = re.sub(r'\[\s+', '[', text)
        text = re.sub(r'\s+\]', ']', text)

        return text

    def _optimize_chinese_english_spacing(self, text: str) -> str:
        """暂时禁用中英文间距优化"""
        # 暂时禁用此功能，因为它可能破坏代码、公式、表格等结构化内容
        # 特别是在流式输出中，AI可能使用特定的格式化策略
        return text

    def _optimize_paragraph_structure(self, text: str, log_prefix: str) -> str:
        """保守的段落结构优化"""
        # 禁用段落优化功能，保留AI输出的原始段落结构
        # 这对于保持格式完整性很重要，特别是在流式处理中
        return text

    def _smart_paragraph_splitting(self, text: str) -> str:
        """智能段落分割 - 更保守的策略，避免过度分割"""
        lines = text.split('\n')
        result = []

        for i, line in enumerate(lines):
            result.append(line)

            # 检查是否需要在当前行后添加段落分隔
            if i < len(lines) - 1:
                current_line = line.strip()
                next_line = lines[i + 1].strip()

                # 更严格的段落分割条件
                if self._should_add_paragraph_break_conservative(current_line, next_line, lines, i):
                    # 检查下一行是否已经有足够的空行分隔
                    if i + 1 < len(lines) and lines[i + 1] != '':
                        result.append('')  # 添加空行分隔段落

        return '\n'.join(result)

    def _should_add_paragraph_break_conservative(self, current_line: str, next_line: str, all_lines: list, current_index: int) -> bool:
        """保守的段落分割判断"""
        if not current_line or not next_line:
            return False

        # 1. 当前行必须以明确的句子结束符结尾
        if not current_line.endswith(('。', '！', '？', '.', '!', '?')):
            return False

        # 2. 下一行不能是特殊格式
        special_formats = ['-', '*', '+', '#', '```', '|', '>', '    ']  # 添加引用和代码缩进
        if any(next_line.startswith(fmt) for fmt in special_formats):
            return False

        # 3. 不能是编号列表
        if re.match(r'^\d+\.', next_line):
            return False

        # 4. 检查语义连续性
        continuity_indicators = [
            # 中文连接词
            '然而', '但是', '不过', '因此', '所以', '因为', '由于', '另外', '此外', '而且', '并且',
            '同时', '接着', '然后', '接下来', '首先', '其次', '最后', '总之', '综上', '例如', '比如',
            '也就是说', '换句话说', '具体来说', '简单来说', '总的来说', '一般来说', '通常来说',
            # 英文连接词
            'however', 'but', 'therefore', 'because', 'moreover', 'furthermore', 'additionally',
            'meanwhile', 'then', 'next', 'first', 'second', 'finally', 'in conclusion', 'for example',
            'that is', 'in other words', 'specifically', 'generally', 'usually', 'typically'
        ]

        for indicator in continuity_indicators:
            if next_line.lower().startswith(indicator.lower()):
                return False

        # 5. 检查当前行长度 - 太短可能不是段落结束
        if len(current_line) < 30:
            return False

        # 6. 检查下一行是否以小写开头（可能是句子延续）
        if next_line and next_line[0].islower():
            return False

        # 7. 检查是否在代码块或引用块内
        if current_index > 0:
            # 向前查找是否在特殊块内
            for j in range(current_index - 1, -1, -1):
                line = all_lines[j].strip()
                if line.startswith('```') or line.startswith('>'):
                    # 检查是否有对应的结束标记
                    for k in range(current_index + 1, len(all_lines)):
                        if all_lines[k].strip().startswith('```'):
                            return False  # 在代码块内
                    break

        # 8. 检查问答格式
        if current_line.endswith(('?', '？')):
            # 下一行可能是答案
            answer_patterns = [
                r'^(是|不|可以|不可以|会|不会|能|不能)',
                r'^(yes|no|it|this|that|the)',
                r'^\d+',  # 数字开头可能是答案
            ]
            for pattern in answer_patterns:
                if re.match(pattern, next_line.lower()):
                    return False

        return True

    def _fix_paragraph_spacing(self, text: str) -> str:
        """修复段落间距"""
        # 限制连续空行数量
        max_newlines = self.typography_config["max_consecutive_newlines"]
        pattern = f'\\n{{{max_newlines + 1},}}'
        replacement = '\n' * max_newlines
        text = re.sub(pattern, replacement, text)

        # 确保标题前后有适当间距
        text = re.sub(r'\n(#{1,6}\s+[^\n]+)\n(?!\n)', r'\n\n\1\n\n', text)

        # 确保代码块前后有适当间距
        text = re.sub(r'\n```([^\n]*)\n', r'\n\n```\1\n', text)
        text = re.sub(r'\n```\n(?!\n)', r'\n```\n\n', text)

        return text

    def _enhance_code_blocks(self, text: str, model_type: str) -> str:
        """增强代码块处理"""
        if '```' not in text:
            return text
        
        lines = text.split('\n')
        result = []
        in_code_block = False
        
        for line in lines:
            if line.strip().startswith('```'):
                if not in_code_block:
                    # 开始代码块 - 智能语言检测
                    lang = line.strip()[3:].strip()
                    if not lang:
                        # 尝试从前面的文本推断语言
                        lang = self._detect_code_language(line, result)
                    result.append(f"```{lang}")
                    in_code_block = True
                else:
                    # 结束代码块
                    result.append("```")
                    in_code_block = False
            else:
                result.append(line)
        
        return '\n'.join(result)
    
    def _detect_code_language(self, code_line: str, context_lines: list) -> str:
        """智能检测代码语言"""
        # 从上下文中查找语言提示
        context_text = ' '.join(context_lines[-5:]).lower()
        
        language_indicators = {
            'python': ['python', 'py', 'import ', 'def ', 'class ', 'print('],
            'javascript': ['javascript', 'js', 'function', 'const ', 'let ', 'var '],
            'java': ['java', 'public class', 'public static', 'import java'],
            'sql': ['sql', 'select', 'from', 'where', 'insert', 'update'],
            'html': ['html', '<div', '<span', '<html', '<body'],
            'css': ['css', 'style', '{', 'color:', 'background:'],
            'bash': ['bash', 'shell', 'echo', 'cd ', 'ls ', 'git '],
            'json': ['json', '{', '}', '":', '[', ']'],
        }
        
        for lang, indicators in language_indicators.items():
            if any(indicator in context_text for indicator in indicators):
                return lang
        
        return ""  # 返回空字符串表示未检测到
    
    def _enhance_list_formatting(self, text: str) -> str:
        """增强列表格式处理"""
        lines = text.split('\n')
        result = []
        
        for i, line in enumerate(lines):
            # 检查是否是列表项
            if re.match(r'^\s*[-*+]\s+', line):
                # 确保列表项前有适当的空行（除非是第一项）
                if i > 0 and result and not result[-1].strip() == '':
                    prev_line = lines[i-1].strip()
                    if prev_line and not re.match(r'^\s*[-*+]\s+', prev_line):
                        result.append('')  # 添加空行分隔
                result.append(line)
            elif re.match(r'^\s*\d+\.\s+', line):
                # 有序列表处理
                if i > 0 and result and not result[-1].strip() == '':
                    prev_line = lines[i-1].strip()
                    if prev_line and not re.match(r'^\s*\d+\.\s+', prev_line):
                        result.append('')  # 添加空行分隔
                result.append(line)
            else:
                result.append(line)
        
        return '\n'.join(result)
    
    def _apply_model_specific_fixes(self, text: str, model_type: str, log_prefix: str) -> str:
        """应用模型特定的修复"""
        if model_type == "gemini":
            text = self._apply_gemini_specific_fixes(text, log_prefix)
        elif model_type == "openai":
            text = self._apply_openai_specific_fixes(text, log_prefix)
        elif model_type == "claude":
            text = self._apply_claude_specific_fixes(text, log_prefix)
        elif model_type == "deepseek":
            text = self._apply_deepseek_specific_fixes(text, log_prefix)
        elif model_type == "qwen":
            text = self._apply_qwen_specific_fixes(text, log_prefix)
        
        return text
    
    def _apply_claude_specific_fixes(self, text: str, log_prefix: str) -> str:
        """应用Claude特定的修复"""
        # Claude经常使用thinking标签
        text = re.sub(r'<thinking>\s*([^<]+)\s*</thinking>', r'**思考过程：**\n\1\n', text, flags=re.DOTALL)
        
        # Claude的artifact处理
        text = re.sub(r'<artifact[^>]*>(.*?)</artifact>', r'```\n\1\n```', text, flags=re.DOTALL)
        
        return text
    
    def _apply_deepseek_specific_fixes(self, text: str, log_prefix: str) -> str:
        """应用DeepSeek特定的修复"""
        # DeepSeek的<think>标签在stream_processor中已经处理，这里主要处理残留
        text = re.sub(r'<think>\s*([^<]+)\s*</think>', r'**推理：**\n\1\n', text, flags=re.DOTALL)
        
        # DeepSeek在代码解释上比较详细，保持其结构
        return text
    
    def _apply_qwen_specific_fixes(self, text: str, log_prefix: str) -> str:
        """应用Qwen特定的修复"""
        # Qwen中英文混排优化
        text = re.sub(r'([\u4e00-\u9fa5])([a-zA-Z0-9])', r'\1 \2', text)
        text = re.sub(r'([a-zA-Z0-9])([\u4e00-\u9fa5])', r'\1 \2', text)
        
        # 中文标点优化
        text = re.sub(r'([，。！？；：])\s+', r'\1', text)
        
        return text
    
    def _apply_content_quality_optimization(self, text: str, model_type: str, log_prefix: str) -> str:
        """应用内容质量优化"""
        # 使用质量评估器进行优化
        try:
            metrics = quality_assessor.assess_quality("", text)
            
            # 根据质量评估结果应用相应优化
            if metrics.formatting_score < 70:
                text = self._apply_formatting_improvements(text)
            
            if metrics.consistency_score < 70:
                text = self._apply_consistency_improvements(text)
            
            if metrics.structure_score < 70:
                text = self._apply_structure_improvements(text)
            
            logger.info(f"{log_prefix}: Quality optimization applied. Score: {metrics.overall_score:.1f}")
            
        except Exception as e:
            logger.warning(f"{log_prefix}: Quality optimization failed: {e}")
        
        return text
    
    def _apply_formatting_improvements(self, text: str) -> str:
        """应用格式化改进"""
        # 确保标题格式正确
        text = re.sub(r'^(#{1,6})([^\s])', r'\1 \2', text, flags=re.MULTILINE)
        
        # 确保列表格式正确
        text = re.sub(r'^([-*+])([^\s])', r'\1 \2', text, flags=re.MULTILINE)
        
        # 确保代码块完整
        code_block_count = text.count('```')
        if code_block_count % 2 != 0:
            text += '\n```'
        
        return text
    
    def _apply_consistency_improvements(self, text: str) -> str:
        """应用一致性改进"""
        # 统一列表标记
        lines = text.split('\n')
        list_marker = None
        result = []
        
        for line in lines:
            if re.match(r'^\s*[-*+]\s+', line):
                if list_marker is None:
                    # 确定使用的列表标记
                    list_marker = re.match(r'^\s*([-*+])', line).group(1)
                # 统一列表标记
                line = re.sub(r'^(\s*)([-*+])(\s+)', f'\\1{list_marker}\\3', line)
            result.append(line)
        
        return '\n'.join(result)
    
    def _apply_structure_improvements(self, text: str) -> str:
        """应用结构改进"""
        # 确保标题层级合理
        lines = text.split('\n')
        result = []
        prev_header_level = 0
        
        for line in lines:
            header_match = re.match(r'^(#{1,6})\s+', line)
            if header_match:
                current_level = len(header_match.group(1))
                
                # 避免跳级过大
                if current_level > prev_header_level + 1 and prev_header_level > 0:
                    # 调整为合适的层级
                    adjusted_level = prev_header_level + 1
                    line = '#' * adjusted_level + line[current_level:]
                
                prev_header_level = len(re.match(r'^(#{1,6})', line).group(1))
            
            result.append(line)
        
        return '\n'.join(result)
    
    def _optimize_paragraph_structure_enhanced(self, text: str, log_prefix: str) -> str:
        """增强的段落结构优化"""
        if not text.strip():
            return text
        
        # 智能段落分割
        paragraphs = text.split('\n\n')
        optimized_paragraphs = []
        
        for paragraph in paragraphs:
            if not paragraph.strip():
                continue
            
            # 检查段落是否过长需要分割
            if len(paragraph) > 500 and '\n' not in paragraph:
                # 尝试在句号处分割
                sentences = paragraph.split('。')
                if len(sentences) > 2:
                    mid_point = len(sentences) // 2
                    part1 = '。'.join(sentences[:mid_point]) + '。'
                    part2 = '。'.join(sentences[mid_point:])
                    if part2 and not part2.endswith('。'):
                        part2 += '。'
                    optimized_paragraphs.extend([part1.strip(), part2.strip()])
                else:
                    optimized_paragraphs.append(paragraph.strip())
            else:
                optimized_paragraphs.append(paragraph.strip())
        
        return '\n\n'.join(filter(None, optimized_paragraphs))
    
    def _optimize_whitespace_enhanced(self, text: str, log_prefix: str) -> str:
        """增强的空白字符优化"""
        # 智能换行处理
        text = re.sub(r'\n{4,}', '\n\n\n', text)  # 限制最多3个连续换行
        
        # 智能空格处理
        text = re.sub(r'[ \t]{2,}', ' ', text)  # 多个空格合并为一个
        
        # 行尾空白清理
        text = re.sub(r'[ \t]+$', '', text, flags=re.MULTILINE)
        
        # 特殊元素前后的空白优化
        # 标题前后的空白
        text = re.sub(r'\n+(#{1,6}\s+[^\n]+)\n+', r'\n\n\1\n\n', text)
        
        # 代码块前后的空白
        text = re.sub(r'\n+(```[^\n]*)\n', r'\n\n\1\n', text)
        text = re.sub(r'\n(```)\n+', r'\n\1\n\n', text)
        
        # 列表前的空白
        text = re.sub(r'\n+([-*+]|\d+\.)\s+', r'\n\n\1 ', text)
        
        return text.strip()
    
    def _apply_smart_typography_enhanced(self, text: str, model_type: str, log_prefix: str) -> str:
        """增强的智能排版处理"""
        # 基于模型类型应用不同的排版策略
        if model_type in ["qwen", "chinese"]:
            # 中文优化排版
            text = self._apply_chinese_typography(text)
        
        # 通用排版优化
        text = self._apply_general_typography(text)
        
        return text
    
    def _apply_chinese_typography(self, text: str) -> str:
        """中文排版优化"""
        # 中英文间距
        text = re.sub(r'([\u4e00-\u9fa5])([a-zA-Z0-9$])', r'\1 \2', text)
        text = re.sub(r'([a-zA-Z0-9$])([\u4e00-\u9fa5])', r'\1 \2', text)
        
        # 中文标点优化
        text = re.sub(r'([，。！？；：])\s+', r'\1', text)
        text = re.sub(r'\s+([，。！？；：])', r'\1', text)
        
        return text
    
    def _apply_general_typography(self, text: str) -> str:
        """通用排版优化"""
        # 英文标点后的空格
        text = re.sub(r'([.!?:;,])([a-zA-Z])', r'\1 \2', text)
        
        # 括号优化
        text = re.sub(r'\(\s+', '(', text)
        text = re.sub(r'\s+\)', ')', text)
        
        # 引号优化
        text = re.sub(r'"\s+([^"]+)\s+"', r'"\1"', text)
        
        return text
    
    def _final_cleanup_enhanced(self, text: str, log_prefix: str) -> str:
        """增强的最终清理"""
        # 基础清理
        text = self._final_cleanup(text, log_prefix)
        
        # 增强清理
        # 移除孤立的标记
        text = re.sub(r'\n\s*[*-]\s*\n', '\n', text)
        
        # 修复可能的标记冲突
        text = re.sub(r'\*\*([^*]*)\*([^*]*)\*\*', r'**\1\2**', text)
        
        # 确保文档结构完整
        if text and not text.endswith('\n'):
            text += '\n'
        
        return text.strip()

# 全局实例
markdown_processor = UnifiedMarkdownProcessor()

def process_content_for_model(text: str, request_id: str = "", model_type: str = "general") -> str:
    """
    便捷函数：处理指定模型类型的内容
    
    Args:
        text: 原始文本
        request_id: 请求ID
        model_type: 模型类型 ("gemini", "openai", "claude", "general")
    
    Returns:
        处理后的文本
    """
    return markdown_processor.process_content(text, request_id, model_type)