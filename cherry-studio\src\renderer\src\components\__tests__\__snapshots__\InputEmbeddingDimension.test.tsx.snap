// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`InputEmbeddingDimension > basic rendering > should match snapshot with all props 1`] = `
<div
  class="ant-space-compact css-dev-only-do-not-override-1261szd"
  style="width: 100%;"
>
  <div
    class="ant-input-number css-dev-only-do-not-override-1261szd ant-input-number-outlined ant-input-number-compact-item ant-input-number-compact-first-item"
    style="flex: 1;"
  >
    <div
      class="ant-input-number-handler-wrap"
    >
      <span
        aria-disabled="false"
        aria-label="Increase Value"
        class="ant-input-number-handler ant-input-number-handler-up"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="up"
          class="anticon anticon-up ant-input-number-handler-up-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
            />
          </svg>
        </span>
      </span>
      <span
        aria-disabled="false"
        aria-label="Decrease Value"
        class="ant-input-number-handler ant-input-number-handler-down"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-input-number-handler-down-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-input-number-input-wrap"
    >
      <input
        aria-valuemin="1"
        aria-valuenow="1536"
        autocomplete="off"
        class="ant-input-number-input"
        placeholder="请输入维度大小"
        role="spinbutton"
        step="1"
        value="1536"
      />
    </div>
  </div>
  <button
    aria-describedby="test-id"
    aria-label="Get embedding dimension"
    class="ant-btn css-dev-only-do-not-override-1261szd ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-compact-item ant-btn-compact-last-item"
    role="button"
    type="button"
  >
    <span
      class="ant-btn-icon"
    >
      <svg
        aria-label="refresh"
        data-testid="refresh-icon"
        role="img"
        size="16"
      >
        RefreshCw
      </svg>
    </span>
  </button>
</div>
`;

exports[`InputEmbeddingDimension > basic rendering > should match snapshot with loading state 1`] = `
<div
  class="ant-space-compact css-dev-only-do-not-override-1261szd"
  style="width: 100%;"
>
  <div
    class="ant-input-number css-dev-only-do-not-override-1261szd ant-input-number-outlined ant-input-number-compact-item ant-input-number-compact-first-item"
    style="flex: 1;"
  >
    <div
      class="ant-input-number-handler-wrap"
    >
      <span
        aria-disabled="false"
        aria-label="Increase Value"
        class="ant-input-number-handler ant-input-number-handler-up"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="up"
          class="anticon anticon-up ant-input-number-handler-up-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
            />
          </svg>
        </span>
      </span>
      <span
        aria-disabled="false"
        aria-label="Decrease Value"
        class="ant-input-number-handler ant-input-number-handler-down"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-input-number-handler-down-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-input-number-input-wrap"
    >
      <input
        aria-valuemin="1"
        autocomplete="off"
        class="ant-input-number-input"
        placeholder="请输入维度大小"
        role="spinbutton"
        step="1"
        value=""
      />
    </div>
  </div>
  <button
    aria-describedby="test-id"
    aria-label="Get embedding dimension"
    class="ant-btn css-dev-only-do-not-override-1261szd ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-loading ant-btn-compact-item ant-btn-compact-last-item"
    role="button"
    type="button"
  >
    <span
      class="ant-btn-icon ant-btn-loading-icon"
    >
      <span
        aria-label="loading"
        class="anticon anticon-loading anticon-spin"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="loading"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="0 0 1024 1024"
          width="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          />
        </svg>
      </span>
    </span>
  </button>
</div>
`;
